.PHONY: help install install-dev lint format test test-coverage clean run docker-build docker-run test-unit test-integration test-system test-demos tools-generate-wallet tools-validate-config tools-scan examples-demo

# Default target
help:
	@echo "Available commands:"
	@echo "  install       - Install production dependencies"
	@echo "  install-dev   - Install development dependencies"
	@echo "  lint          - Run linting (flake8, mypy)"
	@echo "  format        - Format code (black, isort)"
	@echo "  test          - Run all tests"
	@echo "  test-unit     - Run unit tests only"
	@echo "  test-integration - Run integration tests only"
	@echo "  test-system   - Run system validation tests"
	@echo "  test-demos    - Run demo tests"
	@echo "  test-coverage - Run tests with coverage"
	@echo "  clean         - Clean cache and build files"
	@echo "  run           - Run the bot"
	@echo "  setup-env     - Copy .env.example to .env"
	@echo "  tools-generate-wallet - Generate new wallet"
	@echo "  tools-validate-config - Validate system configuration"
	@echo "  tools-scan    - Run market scanner"
	@echo "  examples-demo - Run system demo"

# Installation
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements.txt -e ".[dev]"
	pre-commit install

# Code quality
lint:
	flake8 src/ tests/
	mypy src/
	black --check src/ tests/
	isort --check-only src/ tests/

format:
	black src/ tests/
	isort src/ tests/

# Testing
test:
	pytest tests/ -v

test-unit:
	python -m pytest tests/unit/ -v

test-integration:
	python -m pytest tests/integration/ -v

test-system:
	@echo "Running system validation tests..."
	PYTHONPATH=. python tests/system/test_business_logic.py
	PYTHONPATH=. python tests/system/test_system.py
	PYTHONPATH=. python tests/system/test_telegram_bot.py

test-demos:
	@echo "Running demo tests..."
	python tests/demos/v1_quick_test.py

test-coverage:
	pytest tests/ --cov=src --cov-report=html --cov-report=term

# Environment setup
setup-env:
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "Created .env file from template. Please edit it with your actual values."; \
	else \
		echo ".env file already exists. Please update it manually if needed."; \
	fi

# Cleanup
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf htmlcov/

# Run application
run:
	python -m src.main

# Docker commands
docker-build:
	docker build -t polymarket-copybot .

docker-run:
	docker run --env-file .env polymarket-copybot

# Database commands
db-upgrade:
	alembic upgrade head

db-downgrade:
	alembic downgrade -1

db-revision:
	alembic revision --autogenerate -m "$(message)"

# Tools commands
tools-generate-wallet:
	python tools/generators/wallet_generator.py

tools-validate-config:
	python tools/validators/config_validator.py

tools-alchemy-config:
	python tools/validators/alchemy_config_helper.py

tools-scan:
	python tools/scanners/optimized_scanner.py

# Examples commands  
examples-demo:
	python examples/final_system_demo.py

examples-telegram:
	python examples/start_telegram_bot.py

examples-debug:
	python examples/debug_market_data.py