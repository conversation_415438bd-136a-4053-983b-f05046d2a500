給 AI Agent 開發者的 UI/UX 規格書專案目標： 為一個集成了「策略交易」與「跟單交易」功能的 Polymarket 機器人，設計一套清晰、高效、用戶友好的 Telegram 介面。核心設計原則：整合性： 主選單需清晰區分兩種核心功能，讓用戶能快速進入所需模組。空間效率： 避免單列過長的按鈕列表，優先採用 2xN 的網格佈局，讓介面更緊湊、專業。狀態反饋： 在設定頁面，需要清晰地顯示用戶當前的設定值，並在點擊按鈕後提供即時反饋。引導清晰： 文案需簡潔明瞭，特別是在要求用戶輸入資訊時，指示要非常清楚。頁面佈局規格詳解(頁面一) 主選單 (Main Menu)觸發方式： 用戶發送 /start 指令，或從任何子頁面返回主選單。訊息內容：👋 歡迎回來，[用戶名稱]！

這是您的 Polymarket AI 交易助手。
請選擇您要使用的功能：

📈 **策略機器人** - 運行您預設的高勝率策略。
👥 **跟單機器人** - 複製頂尖交易員的操作。

👇 您也可以管理您的資金和查看績效。
按鈕佈局 (2x3)：[📈 策略機器人]  [👥 跟單機器人]
[💰 我的錢包]    [📊 歷史績效]
[⚙️ 設定]        [❓ 幫助中心]
按鈕功能詳解：📈 策略機器人: 跳轉至 (頁面三) 策略機器人管理。👥 跟單機器人: 跳轉至 (頁面四) 跟單機器人管理。💰 我的錢包: 跳轉至 (頁面二) 錢包狀態總覽。📊 歷史績效: 顯示整體 P&L、勝率等。⚙️ 設定: 進行通知設定、語言設定等。❓ 幫助中心: 顯示常見問題與聯繫方式。(頁面二) 錢包狀態總覽 (Wallet Status)觸發方式： 在主選單點擊 [💰 我的錢包]。訊息內容：💰 **錢包狀態總覽**

🏦 **您的專屬交易錢包地址：**
`[用戶的Polygon錢包地址]`
*(點擊可複製地址)*

---
**💵 當前餘額**

• **USDC (交易資金):** `[即時USDC餘額]`
• **MATIC (網路燃料):** `[即時MATIC餘額]`

🔍 **為何需要這兩種代幣？**
* **USDC** 是您在 Polymarket 上進行預測交易的**本金**。
* **MATIC** 是支付 Polygon 網路交易手續費 (Gas Fee) 的**燃料**，不可或缺。
---
💡 **資金建議**
• **最低啟動:** USDC ≥ $10, MATIC ≥ 0.1
• **順暢交易:** USDC ≥ $50, MATIC ≥ 0.5
按鈕佈局 (2x1 + 1)：[📥 我要充值 (Fund)]  [📤 我要提現 (Withdraw)]
[⬅️ 返回主選單]
按鈕功能詳解：📥 我要充值: 顯示錢包地址的 QR Code，並再次提醒用戶需要充值 USDC 和 MATIC。📤 我要提現: 觸發提現流程，詢問用戶要提現的幣種、金額和目標地址。⬅️ 返回主選單: 返回 (頁面一)。(頁面三) 策略機器人管理 (Strategy Bot Management)觸發方式： 在主選單點擊 [📈 策略機器人]。訊息內容：📈 **策略機器人管理**

**當前狀態：** `[運行中 ✅ / 已停止 ❌]`

**策略摘要：**
• **觸發條件：** 勝率 > `[目前設定的勝率]`% 且 到期日 < `[目前設定的天數]` 天
• **單筆金額：** `[目前設定的金額]` USDC

**今日績效：** `[今日 P&L]` USDC
按鈕佈局 (2x2)：[▶️ 啟動] / [⏹️ 停止]  [🛠️ 修改策略設定]
[📊 查看詳細績效]      [⬅️ 返回主選單]
按鈕功能詳解：▶️ 啟動 / ⏹️ 停止: 根據當前狀態顯示不同按鈕，用於啟動或停止策略。🛠️ 修改策略設定: 跳轉至 (頁面六) 策略機器人設定。📊 查看詳細績效: 顯示此策略的歷史交易紀錄。⬅️ 返回主選單: 返回 (頁面一)。(頁面四) 跟單機器人管理 (Copy Trade Management)觸發方式： 在主選單點擊 [👥 跟單機器人]。訊息內容：👥 **跟單機器人管理**

您目前正在跟單 `[數字]` 位交易員。
選擇下方選項來新增或管理您的跟單設定。

**當前跟單列表：**
1. `[暱稱1]` -> `0x123...abc`
2. `[暱稱2]` -> `0x456...def`
*(如果列表為空，則顯示「您尚未設定任何跟單」)*
按鈕佈局 (1x3)：[➕ 新增跟單]
[✏️ 管理現有跟單]
[⬅️ 返回主選單]
按鈕功能詳解：➕ 新增跟單: 跳轉至 (頁面五) 新增/修改跟單設定。✏️ 管理現有跟單: 顯示當前跟單列表，並提供編輯或刪除按鈕。⬅️ 返回主選單: 返回 (頁面一)。(頁面五) 新增/修改跟單設定 (Add/Edit Copy Trade Config)觸發方式： 在跟單管理頁面點擊 [➕ 新增跟單]。核心機制： 互動式設定頁面。頂部訊息會根據用戶的設定即時更新。用戶點擊按鈕，機器人提問，用戶回答後，訊息和按鈕會刷新。狀態 A：百分比跟單 (預設)訊息內容：➕ **新增跟單設定 (百分比模式)**

**--- 當前配置 ---**
• **🎯 目標錢包:** `[用戶設定的地址]`
• **✍️ 暱稱:** `[用戶設定的暱稱]`
• **📊 跟單類型:** `百分比跟單`
• **🔢 跟單比例:** `[用戶設定的比例]%`
• **💰 最高花費:** `[用戶設定的金額] USDC`
...
**------------------**
按鈕佈局 (2x4 + 2)：[🎯 設定目標錢包]   [✍️ 設定暱稱]
[🔀 切換為固定金額]   [📈 設定跟單比例]
[💰 設定最高花費]   [🚀 設定 Min Trigger]
[💸 設定 Min Spend]   [⚙️ 進階設定]

[✅ 新增跟單]       [❌ 取消]
狀態 B：固定金額跟單訊息內容：➕ **新增跟單設定 (固定金額模式)**

**--- 當前配置 ---**
• **🎯 目標錢包:** `[用戶設定的地址]`
• **✍️ 暱稱:** `[用戶設定的暱稱]`
• **📊 跟單類型:** `固定金額跟單`
• **💵 固定金額:** `[用戶設定的金額] USDC`
...
**------------------**
按鈕佈局 (2x4 + 2)：[🎯 設定目標錢包]   [✍️ 設定暱稱]
[🔀 切換為百分比]   [💵 設定固定金額]
[💰 設定最高花費]   [🚀 設定 Min Trigger]
[💸 設定 Min Spend]   [⚙️ 進階設定]

[✅ 新增跟單]       [❌ 取消]
互動邏輯：點擊 [🔀 切換為固定金額] 會刷新頁面至 狀態 B。點擊 [🔀 切換為百分比] 會刷新頁面至 狀態 A。點擊其他設定按鈕，機器人會要求用戶輸入對應值，輸入後頁面會刷新並顯示新設定。[✅ 新增跟單] 在所有必填項完成後才可點擊，保存設定並返回 (頁面四)。[❌ 取消] 放棄設定並返回 (頁面四)。(頁面六) 策略機器人設定 (Strategy Bot Configuration)觸發方式： 在 (頁面三) 點擊 [🛠️ 修改策略設定]。核心機制： 互動式設定介面。訊息內容：🛠️ **策略機器人設定**

請設定您的自動化交易策略參數。

**--- 當前配置 ---**
• **📈 最低勝率:** `[目前設定值]`% (預設: 85%)
• **🗓️ 最長到期日:** `[目前設定值]` 天 (預設: 1)
• **💰 固定下單金額:** `[目前設定值]` USDC (預設: 10)
**------------------**

👇 請點擊下方按鈕進行修改。
按鈕佈局 (2x2 + 2)：[📈 設定最低勝率]  [🗓️ 設定到期日]
[💰 設定下單金額]  [🔄 恢復預設值]

[✅ 保存設定]      [❌ 取消]
互動邏輯：📈 設定最低勝率: 機器人回覆：「請輸入觸發交易的最低市場勝率 (0-100 之間的數字，例如：85)」。🗓️ 設定到期日: 機器人回覆：「請輸入最長的市場到期天數 (例如：輸入 1 代表只交易 24 小時內到期的市場)」。💰 設定下單金額: 機器人回覆：「請輸入您希望每筆策略交易的固定 USDC 金額 (例如：10)」。🔄 恢復預設值: 將所有參數恢復為系統預設值並刷新訊息。[✅ 保存設定]: 保存當前配置，返回 (頁面三)。[❌ 取消]: 放棄修改，返回 (頁面三)。