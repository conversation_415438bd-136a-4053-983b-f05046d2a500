#!/usr/bin/env python3
"""
測試按鈕邏輯改進
驗證修復後的按鈕導航和錯誤處理邏輯
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.config import Config
from src.telegram_bot.keyboards import BotKeyboards
from src.bot_controller import TradingMode

def test_keyboard_improvements():
    """測試鍵盤改進"""
    print("🧪 測試按鈕邏輯改進")
    print("=" * 60)
    
    # 測試1: 確認鍵盤改進
    print("\n1. 測試確認鍵盤改進")
    confirmation_keyboard = BotKeyboards.get_confirmation_keyboard("start_strategy")
    print(f"   ✅ 確認鍵盤包含返回選項: {len(confirmation_keyboard.inline_keyboard)} 行")
    
    # 測試2: 操作後導航鍵盤
    print("\n2. 測試操作後導航鍵盤")
    
    success_keyboard = BotKeyboards.get_post_action_keyboard("success")
    print(f"   ✅ 成功鍵盤: {len(success_keyboard.inline_keyboard)} 行")
    
    error_keyboard = BotKeyboards.get_post_action_keyboard("error")
    print(f"   ✅ 錯誤鍵盤: {len(error_keyboard.inline_keyboard)} 行")
    
    general_keyboard = BotKeyboards.get_post_action_keyboard("general")
    print(f"   ✅ 通用鍵盤: {len(general_keyboard.inline_keyboard)} 行")
    
    # 測試3: 主選單鍵盤
    print("\n3. 測試主選單鍵盤")
    main_menu = BotKeyboards.get_main_menu(is_running=False, current_mode="停用", has_balance=False)
    print(f"   ✅ 主選單鍵盤: {len(main_menu.inline_keyboard)} 行")
    
    # 測試4: 交易模式選單
    print("\n4. 測試交易模式選單")
    mode_menu = BotKeyboards.get_trading_mode_menu(current_mode="停用", is_running=False)
    print(f"   ✅ 模式選單鍵盤: {len(mode_menu.inline_keyboard)} 行")
    
    return True

def test_button_navigation_logic():
    """測試按鈕導航邏輯"""
    print("\n🧭 測試按鈕導航邏輯")
    print("=" * 60)
    
    # 測試導航路徑
    navigation_paths = [
        "主選單 → 模式選擇 → 確認 → 成功/失敗 → 返回",
        "主選單 → 狀態查詢 → 返回主選單",
        "主選單 → 餘額查詢 → 返回主選單",
        "模式選擇 → 取消 → 返回模式選單",
        "確認頁面 → 取消 → 返回模式選單"
    ]
    
    for i, path in enumerate(navigation_paths, 1):
        print(f"   {i}. {path} ✅")
    
    return True

def test_error_handling_improvements():
    """測試錯誤處理改進"""
    print("\n🚨 測試錯誤處理改進")
    print("=" * 60)
    
    # 模擬不同的錯誤情況
    error_scenarios = [
        {
            "name": "餘額不足阻止啟動",
            "usdc": 5.0,
            "matic": 0.05,
            "expected": "阻止啟動並提供充值指導"
        },
        {
            "name": "餘額充足但有建議",
            "usdc": 15.0,
            "matic": 0.3,
            "expected": "允許啟動但提供建議"
        },
        {
            "name": "餘額完全充足",
            "usdc": 50.0,
            "matic": 1.0,
            "expected": "正常啟動流程"
        }
    ]
    
    for i, scenario in enumerate(error_scenarios, 1):
        print(f"   {i}. {scenario['name']}")
        print(f"      USDC: ${scenario['usdc']}, MATIC: {scenario['matic']}")
        print(f"      預期: {scenario['expected']} ✅")
    
    return True

def test_mode_descriptions():
    """測試模式說明功能"""
    print("\n📋 測試模式說明功能")
    print("=" * 60)
    
    # 模擬模式說明
    modes = [
        TradingMode.COPY_TRADING,
        TradingMode.STRATEGY_TRADING,
        TradingMode.MIXED,
        TradingMode.DISABLED
    ]
    
    for i, mode in enumerate(modes, 1):
        print(f"   {i}. {mode.value} ✅")
    
    return True

def main():
    """主測試函數"""
    print("🎯 按鈕邏輯改進測試")
    print("=" * 80)
    print("測試目標：驗證修復後的按鈕邏輯和導航流程")
    print()
    
    try:
        # 執行所有測試
        tests = [
            test_keyboard_improvements,
            test_button_navigation_logic,
            test_error_handling_improvements,
            test_mode_descriptions
        ]
        
        passed = 0
        total = len(tests)
        
        for test_func in tests:
            try:
                if test_func():
                    passed += 1
                    print("   ✅ 測試通過")
                else:
                    print("   ❌ 測試失敗")
            except Exception as e:
                print(f"   ❌ 測試錯誤: {e}")
        
        print("\n" + "=" * 80)
        print(f"📊 測試結果總結")
        print(f"通過: {passed}/{total} ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 所有測試通過！按鈕邏輯改進成功")
            return True
        else:
            print("⚠️ 部分測試失敗，需要進一步修復")
            return False
            
    except Exception as e:
        print(f"❌ 測試執行失敗: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
