#!/usr/bin/env python3
"""
Telegram Bot System Validation Script
驗證 Telegram Bot 系統的基礎設施和依賴關係

執行方式: python validate_telegram_bot.py
"""

import asyncio
import os
import sys
from typing import Dict, List, Tuple

try:
    import telegram
    from telegram import Bot
    from telegram.ext import Application, CommandHandler, ContextTypes
    from telegram.error import InvalidToken, NetworkError
    print("✅ python-telegram-bot 依賴已正確安裝")
except ImportError as e:
    print(f"❌ Telegram Bot 依賴導入失敗: {e}")
    sys.exit(1)

from src.core.config import Config
from src.bot_controller import BotController

class TelegramBotValidator:
    """Telegram Bot 系統驗證器"""

    def __init__(self):
        """初始化驗證器"""
        self.config = Config()
        self.validation_results: List[Tuple[str, bool, str]] = []

    def add_result(self, test_name: str, success: bool, message: str):
        """添加驗證結果"""
        self.validation_results.append((test_name, success, message))
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")

    def validate_environment_variables(self) -> bool:
        """驗證環境變數配置"""
        print("\n🔍 驗證 Telegram Bot 環境變數...")

        # 檢查必要的環境變數
        required_vars = {
            "TELEGRAM_BOT_TOKEN": "Telegram Bot Token",
            "TELEGRAM_CHAT_ID": "Telegram Chat ID (可選，用於通知)",
        }

        success = True
        for var_name, description in required_vars.items():
            value = os.getenv(var_name)
            if var_name == "TELEGRAM_BOT_TOKEN":
                if value:
                    # 驗證 token 格式
                    if ":" in value and len(value.split(":")[0]) >= 8:
                        self.add_result(
                            f"環境變數 {var_name}",
                            True,
                            f"{description} 已配置且格式正確"
                        )
                    else:
                        self.add_result(
                            f"環境變數 {var_name}",
                            False,
                            f"{description} 格式不正確 (應為 bot_id:token 格式)"
                        )
                        success = False
                else:
                    self.add_result(
                        f"環境變數 {var_name}",
                        False,
                        f"{description} 未配置 (必須設置)"
                    )
                    success = False
            else:
                # 可選環境變數
                if value:
                    self.add_result(
                        f"環境變數 {var_name}",
                        True,
                        f"{description} 已配置"
                    )
                else:
                    self.add_result(
                        f"環境變數 {var_name}",
                        True,
                        f"{description} 未配置 (可選)"
                    )

        return success

    async def validate_bot_token(self) -> bool:
        """驗證 Bot Token 有效性"""
        print("\n🔍 驗證 Telegram Bot Token...")

        token = os.getenv("TELEGRAM_BOT_TOKEN")
        if not token:
            self.add_result(
                "Bot Token 驗證",
                False,
                "未配置 TELEGRAM_BOT_TOKEN 環境變數"
            )
            return False

        try:
            # 創建 Bot 實例並獲取基本信息
            bot = Bot(token=token)
            bot_info = await bot.get_me()

            self.add_result(
                "Bot Token 驗證",
                True,
                f"Bot 連接成功: @{bot_info.username} ({bot_info.first_name})"
            )

            # 驗證 Bot 權限
            try:
                await bot.get_my_commands()
                self.add_result(
                    "Bot 權限檢查",
                    True,
                    "Bot 具有必要的 API 權限"
                )
            except Exception as e:
                self.add_result(
                    "Bot 權限檢查",
                    False,
                    f"Bot 權限檢查失敗: {e}"
                )
                return False

            return True

        except InvalidToken:
            self.add_result(
                "Bot Token 驗證",
                False,
                "Token 無效或格式錯誤"
            )
            return False
        except NetworkError as e:
            self.add_result(
                "Bot Token 驗證",
                False,
                f"網路連接錯誤: {e}"
            )
            return False
        except Exception as e:
            self.add_result(
                "Bot Token 驗證",
                False,
                f"未知錯誤: {e}"
            )
            return False

    def validate_telegram_bot_structure(self) -> bool:
        """驗證 Telegram Bot 文件結構"""
        print("\n🔍 驗證 Telegram Bot 文件結構...")

        base_path = "src/telegram_bot"

        # 預期的文件結構
        expected_files = {
            "__init__.py": "模塊初始化文件",
            "handlers.py": "命令處理器",
            "keyboards.py": "鍵盤介面",
            "notifications.py": "通知系統",
            "bot.py": "Bot 主要邏輯",
        }

        success = True
        for filename, description in expected_files.items():
            file_path = os.path.join(base_path, filename)
            if os.path.exists(file_path):
                self.add_result(
                    f"文件結構 {filename}",
                    True,
                    f"{description} 存在"
                )
            else:
                self.add_result(
                    f"文件結構 {filename}",
                    False,
                    f"{description} 不存在，需要創建"
                )
                if filename != "__init__.py":  # __init__.py 已存在
                    success = False

        return success

    def validate_integration_readiness(self) -> bool:
        """驗證與現有系統的集成準備狀態"""
        print("\n🔍 驗證系統集成準備狀態...")

        # 檢查 BotController 是否支持 Telegram 集成
        try:
            # 檢查 BotController 類定義
            import inspect
            controller_methods = [method for method in dir(BotController)
                                if not method.startswith('_')]

            # 檢查是否有 Telegram 相關方法
            telegram_methods = [method for method in controller_methods
                              if 'telegram' in method.lower() or 'notify' in method.lower()]

            # 檢查關鍵方法是否存在
            required_methods = ['start_trading', 'stop_trading', 'get_status']
            missing_methods = [method for method in required_methods
                             if method not in controller_methods]

            if missing_methods:
                self.add_result(
                    "BotController 集成",
                    False,
                    f"BotController 缺少必要方法: {missing_methods}"
                )
                return False

            self.add_result(
                "BotController 集成",
                True,
                f"BotController 類結構正確，包含 {len(controller_methods)} 個方法"
            )

        except Exception as e:
            self.add_result(
                "BotController 集成",
                False,
                f"BotController 導入或初始化失敗: {e}"
            )
            return False

        # 檢查必要的核心模塊
        core_modules = [
            ("src.core.config", "配置管理"),
            ("src.trading.copy_trader", "V2 跟單系統"),
            ("src.trading.strategy_trader", "V1 策略系統"),
            ("src.database.repository", "數據庫操作"),
        ]

        integration_ready = True
        for module_path, description in core_modules:
            try:
                __import__(module_path)
                self.add_result(
                    f"核心模塊 {description}",
                    True,
                    f"{module_path} 可正常導入"
                )
            except ImportError as e:
                self.add_result(
                    f"核心模塊 {description}",
                    False,
                    f"{module_path} 導入失敗: {e}"
                )
                integration_ready = False

        return integration_ready

    
    async def run_validation(self) -> Dict[str, any]:
        """執行完整的驗證流程"""
        print("🚀 開始 Telegram Bot 系統驗證...")
        print("=" * 60)
        
        # 執行各項驗證
        env_success = self.validate_environment_variables()
        token_success = await self.validate_bot_token() if env_success else False
        structure_success = self.validate_telegram_bot_structure()
        integration_success = self.validate_integration_readiness()
        
        # 計算總體健康度
        total_tests = len(self.validation_results)
        passed_tests = sum(1 for _, success, _ in self.validation_results if success)
        health_percentage = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        # 輸出總結
        print("\n" + "=" * 60)
        print("📊 Telegram Bot 系統驗證總結")
        print("=" * 60)
        
        for test_name, success, message in self.validation_results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status}: {test_name}")
            if not success:
                print(f"    原因: {message}")
        
        print(f"\n📈 總體健康度: {health_percentage:.1f}% ({passed_tests}/{total_tests})")
        
        # 準備就緒評估
        if health_percentage >= 80 and token_success:
            readiness_status = "🎯 準備就緒 - 可以開始開發 Telegram Bot"
        elif health_percentage >= 60:
            readiness_status = "⚠️  基本就緒 - 需要修復一些問題"
        else:
            readiness_status = "🚨 未準備就緒 - 需要解決重大問題"
        
        print(f"\n{readiness_status}")
        
        # 下一步建議
        if health_percentage < 100:
            print("\n📋 建議的下一步行動:")
            if not env_success:
                print("1. 配置 TELEGRAM_BOT_TOKEN 環境變數")
            if not token_success:
                print("2. 驗證 Bot Token 有效性")
            if not structure_success:
                print("3. 創建缺失的 Telegram Bot 文件")
            if not integration_success:
                print("4. 修復系統集成問題")
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "health_percentage": health_percentage,
            "env_success": env_success,
            "token_success": token_success,
            "structure_success": structure_success,
            "integration_success": integration_success,
            "ready_for_development": health_percentage >= 80 and token_success
        }

async def main():
    """主函數"""
    try:
        validator = TelegramBotValidator()
        results = await validator.run_validation()
        
        # 根據結果返回適當的退出碼
        if results["ready_for_development"]:
            print("\n🎉 驗證完成！Telegram Bot 系統準備就緒。")
            sys.exit(0)
        else:
            print("\n⚠️  驗證完成，但發現需要解決的問題。")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  驗證被用戶中斷")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ 驗證過程中發生未預期錯誤: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    # 設置事件循環策略 (解決 Windows 兼容性問題)
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())