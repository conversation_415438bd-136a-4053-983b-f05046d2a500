#!/usr/bin/env python3
"""
V2 跟單系統功能驗證腳本
驗證 Mempool 掃描、Dune Analytics 集成和跟單交易邏輯
"""

import asyncio

from src.core.config import Config, BotMode
from src.utils.logging_config import get_logger
from src.scanning.dune_client import DuneClient
from src.database.repository import Repository
from src.scanning.mempool_scanner import MempoolScanner, ParsedTradeData
from src.trading.copy_trader import CopyTrader
from src.trading.polymarket_client import PolymarketClient
from src.web3_tools.wallet import WalletManager
from src.web3_tools.transactions import TransactionManager
from src.bot_controller import BotController

logger = get_logger(__name__)

class V2SystemValidator:
    """V2 跟單系統功能驗證器"""

    def __init__(self):
        self.config = Config()
        self.results = {}

    async def validate_all(self):
        """驗證所有V2系統功能"""
        print("🚀 開始 V2 跟單系統功能驗證")
        print("=" * 60)

        await self.validate_dune_client()
        await self.validate_mempool_scanner()
        await self.validate_copy_trader()
        await self.validate_integration()

        self.print_summary()

    async def validate_dune_client(self):
        """驗證 Dune Analytics 客戶端"""
        print("\n📊 驗證 Dune Analytics 客戶端...")

        try:
            # 初始化依賴組件
            repo = Repository(self.config)

            # 初始化客戶端
            dune = DuneClient(self.config, repo)
            print("✅ Dune 客戶端初始化成功")

            # 測試連接（不執行真實查詢，避免API配額）
            # 只驗證配置和方法存在
            assert hasattr(dune, 'get_smart_wallets'), "缺少 get_smart_wallets 方法"
            print("✅ Dune 客戶端API方法檢查通過")

            # 驗證配置
            assert dune.api_key, "API密鑰未配置"
            assert dune.query_ids, "查詢ID未配置"
            print("✅ Dune 客戶端配置檢查通過")

            self.results['dune_client'] = True

        except Exception as e:
            print(f"❌ Dune 客戶端驗證失敗: {e}")
            self.results['dune_client'] = False

    async def validate_mempool_scanner(self):
        """驗證 Mempool 掃描器"""
        print("\n🔍 驗證 Mempool 掃描器...")

        try:
            # 初始化組件
            repo = Repository(self.config)
            scanner = MempoolScanner(self.config, repo)
            print("✅ Mempool 掃描器初始化成功")

            # 驗證核心方法
            assert hasattr(scanner, 'add_wallet_to_monitor'), "缺少錢包監控方法"
            assert hasattr(scanner, 'start_scanning'), "缺少掃描啟動方法"
            print("✅ Mempool 掃描器方法檢查通過")

            # 測試錢包添加
            test_wallet = "******************************************"
            scanner.add_wallet_to_monitor(test_wallet)
            assert test_wallet.lower() in [w.lower() for w in scanner.monitored_wallets]
            print("✅ 錢包監控功能測試通過")

            # 驗證ParsedTradeData結構
            trade_data_fields = ['tx_hash', 'condition_id', 'amount_usdc', 'side']
            missing_fields = [field for field in trade_data_fields if not hasattr(ParsedTradeData, '__annotations__') or field not in ParsedTradeData.__annotations__]
            if not missing_fields:
                print("✅ 交易數據結構檢查通過")
            else:
                raise AttributeError(f"缺少交易數據字段: {missing_fields}")

            self.results['mempool_scanner'] = True

        except Exception as e:
            print(f"❌ Mempool 掃描器驗證失敗: {e}")
            self.results['mempool_scanner'] = False

    async def validate_copy_trader(self):
        """驗證跟單交易器"""
        print("\n🔄 驗證跟單交易器...")

        try:
            # 初始化依賴組件
            repo = Repository(self.config)
            wallet_manager = WalletManager(self.config)
            transaction_manager = TransactionManager(self.config, wallet_manager)
            client = PolymarketClient(self.config, wallet_manager, transaction_manager)

            # 初始化交易器
            copy_trader = CopyTrader(self.config, repo, wallet_manager, client)
            print("✅ 跟單交易器初始化成功")

            # 驗證核心方法
            assert hasattr(copy_trader, 'start_copy_trading'), "缺少跟單啟動方法"
            assert hasattr(copy_trader, 'initialize_components'), "缺少組件初始化方法"
            print("✅ 跟單交易器方法檢查通過")

            # 驗證Polymarket客戶端集成
            assert hasattr(client, 'execute_buy_order'), "缺少買入方法"
            assert hasattr(client, 'execute_sell_order'), "缺少賣出方法"
            assert hasattr(transaction_manager, 'execute_approve_transaction'), "缺少USDC授權方法"
            print("✅ Polymarket 客戶端集成檢查通過")

            self.results['copy_trader'] = True

        except Exception as e:
            print(f"❌ 跟單交易器驗證失敗: {e}")
            self.results['copy_trader'] = False

    async def validate_integration(self):
        """驗證系統集成"""
        print("\n🔗 驗證系統集成...")

        try:
            # 初始化依賴組件
            repo = Repository(self.config)
            wallet_manager = WalletManager(self.config)
            transaction_manager = TransactionManager(self.config, wallet_manager)
            polymarket_client = PolymarketClient(self.config, wallet_manager, transaction_manager)

            # 初始化控制器
            controller = BotController(self.config, repo, wallet_manager, transaction_manager, polymarket_client)
            print("✅ 系統控制器初始化成功")

            # 驗證模式切換
            assert BotMode.COPY_TRADING in BotMode, "缺少跟單模式"
            print("✅ 模式配置檢查通過")

            # 驗證組件協調
            assert hasattr(controller, 'start_trading'), "缺少啟動方法"
            assert hasattr(controller, 'stop_trading'), "缺少停止方法"
            print("✅ 組件協調方法檢查通過")

            self.results['integration'] = True

        except Exception as e:
            print(f"❌ 系統集成驗證失敗: {e}")
            self.results['integration'] = False

    
    def print_summary(self):
        """打印驗證總結"""
        print("\n" + "=" * 80)
        print("🎯 V2 跟單系統功能驗證總結")
        print("=" * 80)
        
        passed = sum(1 for v in self.results.values() if v)
        total = len(self.results)
        
        print(f"總功能模組: {total}")
        print(f"通過驗證: {passed} ✅")
        print(f"失敗: {total - passed} ❌")
        print(f"成功率: {(passed/total*100):.1f}%")
        
        print("\n📊 詳細結果:")
        status_map = {
            'dune_client': 'Dune Analytics 客戶端',
            'mempool_scanner': 'Mempool 掃描器',
            'copy_trader': '跟單交易器',
            'integration': '系統集成'
        }
        
        for key, result in self.results.items():
            status = "✅" if result else "❌"
            name = status_map.get(key, key)
            print(f"{status} {name}")
        
        if passed == total:
            print("\n🎉 V2 跟單系統所有功能驗證通過！")
            print("👍 可以開始進行實際交易測試")
        else:
            print(f"\n⚠️ 還有 {total - passed} 個功能需要修復")
            print("🔧 建議先完成失敗模組的修復工作")
        
        print("=" * 80)

async def main():
    """主驗證函數"""
    validator = V2SystemValidator()
    await validator.validate_all()

if __name__ == "__main__":
    asyncio.run(main())