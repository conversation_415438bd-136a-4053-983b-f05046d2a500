2025-09-16 18:09:00,675 - src.database.repository - ERROR - [2m2025-09-16T10:09:00.675169Z[0m [[31m[1merror    [0m] [1mDatabase session error: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')[0m [[0m[1m[34msrc.database.repository[0m][0m
2025-09-16 18:09:00,677 - __main__ - ERROR - [2m2025-09-16T10:09:00.677813Z[0m [[31m[1merror    [0m] [1m❌ Bot initialization failed: Database health check failed: {'status': 'unhealthy', 'database_connected': False, 'error': "Database operation failed: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')", 'timestamp': '2025-09-16T10:09:00.677775+00:00'}[0m [[0m[1m[34m__main__[0m][0m
2025-09-16 18:10:28,134 - src.web3_tools.wallet - ERROR - [2m2025-09-16T10:10:28.133855Z[0m [[31m[1merror    [0m] [1mFailed to get network info: The field extraData is 289 bytes, but should be 32. It is quite likely that you are connected to a POA chain. Refer to http://web3py.readthedocs.io/en/stable/middleware.html#proof-of-authority for more details. The full extraData is: HexBytes('0xd783010f0b83626f7288676f312e32342e36856c696e75780000000000000000f8be80f8bbc0c0c0c0c0c180c105c106c0c108c0c107c108c2040cc10bc109c10fc110c10ec111c112c10dc115c116c114c118c3050b19c119c0c0c0c0c10cc117c121c122c4800a0712c42021240dc22523c21b1ac127c128c22725c12ac22a29c12bc12cc12cc12ec0c125c0c0c0c0c0c0c23226c139c0c136c0c0c121c13ac140c139c22f2dc108c24330c0c145c147c24348c148c14ac144c0c24142c14cc149c14fc151c150c153c154c155c156c157c158c159c15ac15bc152c15cc15ed22a9a523ac4e0ae4251994b7a0a1a6e27e9f1713c1fdd897d3e3af5e165b4a96c5eee9ee5ed2ff7dced93f18517c52a455628133aa0d7802df03af023acf60401')[0m [[0m[1m[34msrc.web3_tools.wallet[0m][0m
2025-09-16 18:10:28,142 - __main__ - ERROR - [2m2025-09-16T10:10:28.142261Z[0m [[31m[1merror    [0m] [1m❌ Bot initialization failed: Wallet not connected to network[0m [[0m[1m[34m__main__[0m][0m
2025-09-16 18:13:54,151 - __main__ - ERROR - [2m2025-09-16T10:13:54.151236Z[0m [[31m[1merror    [0m] [1m演示過程中發生錯誤: BotController.__init__() missing 1 required positional argument: 'polymarket_client'[0m [[0m[1m[34m__main__[0m][0m
2025-09-16 18:14:33,384 - __main__ - ERROR - [2m2025-09-16T10:14:33.384906Z[0m [[31m[1merror    [0m] [1m演示過程中發生錯誤: object dict can't be used in 'await' expression[0m [[0m[1m[34m__main__[0m][0m
2025-09-16 18:16:37,840 - __main__ - ERROR - [2m2025-09-16T10:16:37.840392Z[0m [[31m[1merror    [0m] [1m演示過程中發生錯誤: type object 'TradingMode' has no attribute 'MIXED'[0m [[0m[1m[34m__main__[0m][0m
2025-09-16 18:26:33,004 - src.scanning.market_scanner - ERROR - [2m2025-09-16T10:26:33.004494Z[0m [[31m[1merror    [0m] [1mMarket scan failed: GraphQL query failed: 401 - invalid token/cookies
[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 18:38:21,510 - src.scanning.market_scanner - ERROR - [2m2025-09-16T10:38:21.510293Z[0m [[31m[1merror    [0m] [1mMarket scan failed: GraphQL query failed: 401 - invalid token/cookies
[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 18:43:22,464 - src.scanning.market_scanner - ERROR - [2m2025-09-16T10:43:22.464447Z[0m [[31m[1merror    [0m] [1mMarket scan failed: GraphQL query failed: 401 - invalid token/cookies
[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,432 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.432591Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,434 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.434471Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,436 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.436036Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,437 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.437772Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,439 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.439467Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,441 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.441021Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,442 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.442496Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,444 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.444311Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,445 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.445791Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,447 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.447465Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,449 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.449341Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,451 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.451125Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,452 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.452937Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,454 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.454897Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,456 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.456701Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,458 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.458501Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,460 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.460264Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,462 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.462005Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,463 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.463792Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,465 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.465626Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,467 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.467361Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,469 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.469193Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,471 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.470976Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,472 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.472765Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,474 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.474529Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,476 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.476350Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,478 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.478150Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,479 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.479930Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,481 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.481722Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,483 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.483473Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,485 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.485327Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,487 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.487249Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,489 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.489109Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,490 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.490905Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,492 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.492723Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,494 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.494514Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,496 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.496286Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,498 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.498062Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,499 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.499797Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,501 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.501525Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,503 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.503321Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,505 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.505114Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,506 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.506924Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,508 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.508831Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,510 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.510585Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,512 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.512368Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,514 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.514165Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,516 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.516858Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,519 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.519157Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,521 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.521155Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,523 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.522987Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,525 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.524967Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,527 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.527196Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,529 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.529188Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,531 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.531142Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,533 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.533056Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,535 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.535111Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,537 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.537308Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,539 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.539580Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,541 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.541593Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,543 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.543560Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,545 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.545372Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,547 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.547208Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,549 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.549123Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,550 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.550906Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,552 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.552704Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,554 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.554632Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,556 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.556570Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,558 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.558642Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,561 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.561888Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,563 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.563932Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,565 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.565805Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,567 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.567648Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,569 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.569497Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,571 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.571341Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,573 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.573186Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,575 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.575042Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,577 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.577034Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,579 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.579113Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,582 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.582572Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,585 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.585007Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,587 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.587060Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,589 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.589273Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,591 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.591489Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,593 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.593462Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,595 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.595773Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,598 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.598771Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,601 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.601019Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,603 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.602998Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,604 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.604910Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,606 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.606692Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,608 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.608615Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,610 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.610460Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,612 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.612296Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,614 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.614303Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,616 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.616371Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,618 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.618532Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,620 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.620860Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,622 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.622784Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:09:57,624 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:09:57.624566Z[0m [[31m[1merror    [0m] [1mError analyzing market: can't subtract offset-naive and offset-aware datetimes[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:36,992 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:36.992854Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:36,994 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:36.994563Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:36,996 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:36.996110Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:36,997 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:36.997822Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:36,999 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:36.999798Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,001 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.001476Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,003 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.003094Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,004 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.004904Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,006 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.006532Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,008 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.008443Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,010 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.010455Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,012 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.012322Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,014 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.014175Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,016 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.016043Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,017 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.017846Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,019 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.019687Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,021 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.021641Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,023 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.023517Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,025 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.025380Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,027 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.027280Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,029 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.029121Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,031 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.030968Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,032 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.032821Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,034 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.034626Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,036 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.036493Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,038 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.038505Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,040 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.040417Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,042 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.042298Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,044 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.044203Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,046 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.046200Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,048 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.048253Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,050 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.050325Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,052 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.052247Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,054 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.054119Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,056 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.056073Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,057 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.057940Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,059 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.059792Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,061 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.061662Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,063 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.063532Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,065 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.065381Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,067 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.067226Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,069 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.069092Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,071 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.070953Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,073 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.072987Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,074 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.074819Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,076 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.076701Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,078 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.078588Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,080 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.080452Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,082 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.082292Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,084 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.084146Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,086 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.086009Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,087 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.087870Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,089 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.089734Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,091 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.091604Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,093 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.093540Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,095 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.095460Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,097 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.097346Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,099 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.099274Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,101 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.101157Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,103 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.103053Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,104 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.104906Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,106 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.106747Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,108 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.108554Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,110 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.110388Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,112 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.112228Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,114 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.114080Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,115 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.115927Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,117 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.117776Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,119 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.119522Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,121 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.121311Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,123 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.123196Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,125 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.125002Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,126 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.126820Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,128 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.128680Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,130 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.130548Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,132 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.132923Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,134 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.134865Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,136 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.136729Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,138 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.138593Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,140 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.140440Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,142 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.142382Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,144 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.144257Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,146 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.146255Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,148 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.148075Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,149 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.149883Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,151 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.151720Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,153 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.153574Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,155 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.155433Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,157 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.157282Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,159 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.159096Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,160 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.160889Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,162 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.162806Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,164 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.164683Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,166 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.166662Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,168 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.168546Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,170 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.170369Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,172 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.172247Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,174 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.174178Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,176 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.176005Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:10:37,177 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:10:37.177827Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,460 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.460838Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,462 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.462698Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,464 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.464259Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,466 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.466279Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,468 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.468475Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,470 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.470121Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,471 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.471783Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,473 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.473666Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,475 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.475533Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,477 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.477356Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,479 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.479281Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,481 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.481104Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,483 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.482963Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,484 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.484853Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,486 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.486667Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,488 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.488489Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,490 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.490502Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,492 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.492427Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,494 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.494266Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,496 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.496138Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,497 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.497926Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,499 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.499741Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,501 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.501617Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,503 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.503479Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,505 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.505295Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,507 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.507140Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,509 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.509068Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,511 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.510957Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,512 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.512803Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,514 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.514601Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,516 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.516443Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,518 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.518408Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,520 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.520243Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,522 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.522079Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,523 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.523902Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,525 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.525803Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,527 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.527671Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,529 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.529549Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,531 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.531366Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,533 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.533253Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,535 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.535173Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,538 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.537975Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,540 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.540555Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,542 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.542751Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,544 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.544925Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,546 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.546831Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,548 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.548546Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,550 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.550577Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,552 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.552699Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,554 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.554437Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,557 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.557302Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,560 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.560390Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,564 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.564528Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,567 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.567307Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,569 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.569424Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,571 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.571533Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,574 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.574061Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,576 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.576426Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,578 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.578430Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,580 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.580172Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,581 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.581824Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,583 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.583776Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,586 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.586441Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,589 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.589472Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,591 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.591713Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,594 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.594102Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,597 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.597519Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,599 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.599545Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,601 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.601526Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,603 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.603388Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,605 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.605417Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,607 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.607727Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,609 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.609803Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,612 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.612059Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,616 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.615971Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,618 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.618235Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,620 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.620246Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,622 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.622116Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,624 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.624586Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,626 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.626597Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,628 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.628558Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,630 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.630486Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,632 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.632599Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,634 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.634768Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,637 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.637039Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,639 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.639163Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,641 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.641031Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,643 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.643063Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,645 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.645344Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,647 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.647484Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,649 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.649421Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,651 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.651404Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,653 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.653324Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,655 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.655419Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,657 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.657498Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,659 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.659602Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,662 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.662235Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:14:01,665 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:14:01.665644Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:16:44,465 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:16:44.465218Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:16:44,468 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:16:44.468634Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:16:44,473 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:16:44.473706Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:16:44,479 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:16:44.479135Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:16:44,492 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:16:44.492743Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:16:44,496 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:16:44.496692Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:16:44,498 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:16:44.498928Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:16:44,500 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:16:44.500703Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:16:44,502 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:16:44.502467Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:16:44,504 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:16:44.504198Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:17:23,163 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:17:23.163334Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:17:23,168 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:17:23.167977Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:17:23,174 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:17:23.174880Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:17:23,181 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:17:23.180954Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:17:23,198 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:17:23.197922Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:17:23,204 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:17:23.204705Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:17:23,208 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:17:23.208400Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:17:23,211 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:17:23.211250Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:17:23,214 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:17:23.214104Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:17:23,217 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:17:23.217051Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,267 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.267756Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,269 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.269832Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,271 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.271590Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,273 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.273528Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,275 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.275451Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,277 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.277262Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,279 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.279165Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,281 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.281072Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,283 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.282961Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,285 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.284948Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,286 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.286871Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,288 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.288734Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,290 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.290595Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,292 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.292445Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,294 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.294240Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,296 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.296074Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,297 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.297930Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,299 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.299723Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,301 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.301567Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,303 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.303397Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,305 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.305277Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,307 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.307131Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,309 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.308967Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,310 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.310758Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,312 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.312570Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,314 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.314457Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,316 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.316307Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,318 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.318219Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,320 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.320086Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,321 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.321929Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,323 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.323764Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,325 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.325636Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,327 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.327513Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,329 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.329432Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,331 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.331295Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,333 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.333093Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,334 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.334898Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,336 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.336746Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,338 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.338653Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,340 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.340540Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,342 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.342405Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,344 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.344318Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,346 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.346170Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,348 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.348185Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,350 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.350765Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,353 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.353123Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,355 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.355242Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,357 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.357359Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,359 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.359541Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,361 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.361843Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,363 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.363572Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,365 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.365576Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,367 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.367648Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,370 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.369956Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,373 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.373437Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,375 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.375803Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,378 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.378050Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,380 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.380300Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,382 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.382502Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,384 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.384266Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,386 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.386275Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,390 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.389943Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,392 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.392740Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,395 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.395047Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,397 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.397044Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,399 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.399105Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,401 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.401107Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,403 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.403095Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,405 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.405167Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,407 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.407783Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,412 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.412039Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,414 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.414774Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,416 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.416859Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,418 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.418635Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,420 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.420589Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,422 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.422567Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,424 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.424588Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,426 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.426585Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,428 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.428668Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,430 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.430880Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,434 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.434271Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,436 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.436165Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,438 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.438171Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,440 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.440147Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,442 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.442093Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,444 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.444013Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,445 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.445869Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,447 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.447893Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,450 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.450180Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,452 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.452466Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,454 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.454541Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,456 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.456433Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,458 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.458430Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,460 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.460653Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,462 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.462862Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,465 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.465052Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,467 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.467238Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:19:03,469 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:19:03.469342Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:01,890 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:01.890847Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:01,895 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:01.895526Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:01,901 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:01.900947Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:01,906 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:01.906785Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:01,921 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:01.921429Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:01,925 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:01.925623Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:01,928 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:01.928431Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:01,930 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:01.930634Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:01,933 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:01.932957Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:01,935 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:01.935239Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,646 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.646466Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,648 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.648265Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,649 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.649895Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,651 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.651708Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,653 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.653799Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,655 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.655610Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,657 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.657488Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,659 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.659406Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,661 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.661238Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,663 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.663152Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,665 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.665106Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,667 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.666975Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,668 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.668844Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,670 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.670741Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,672 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.672575Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,674 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.674422Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,676 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.676288Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,678 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.678130Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,680 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.679982Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,681 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.681838Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,683 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.683670Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,685 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.685531Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,687 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.687410Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,689 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.689461Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,691 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.691437Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,693 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.693340Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,695 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.695190Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,697 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.697044Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,698 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.698930Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,700 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.700755Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,702 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.702628Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,704 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.704476Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,706 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.706306Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,708 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.708155Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,710 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.710053Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,712 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.711947Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,713 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.713789Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,716 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.716622Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,719 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.718956Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,721 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.721021Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,723 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.723006Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,725 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.725107Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,727 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.727125Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,729 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.729047Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,731 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.730958Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,732 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.732827Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,734 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.734880Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,737 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.737166Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,739 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.739428Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,741 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.741631Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,743 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.743585Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,745 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.745525Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,747 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.747509Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,749 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.749386Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,751 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.751317Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,753 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.753361Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,755 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.755621Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,758 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.757970Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,760 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.760281Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,762 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.762332Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,764 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.764402Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,766 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.766421Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,768 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.768318Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,770 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.770253Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,772 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.772201Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,774 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.774260Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,777 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.777572Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,779 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.779831Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,781 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.781881Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,783 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.783795Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,785 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.785619Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,787 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.787546Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,789 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.789413Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,791 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.791297Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,793 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.793266Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,795 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.795473Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,797 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.797769Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,800 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.800015Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,802 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.802070Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,804 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.804091Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,806 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.806120Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,808 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.808002Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,810 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.810030Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,812 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.811981Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,813 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.813933Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,816 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.816110Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,818 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.818306Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,820 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.820404Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,822 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.822425Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,824 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.824327Z[0m [[31m[1merror    [0m] [1mError analyzing market: 'endDate'[0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,826 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.826170Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,828 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.828146Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,830 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.830074Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,831 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.831938Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,833 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.833819Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,835 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.835851Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,837 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.837745Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-16 19:24:04,839 - src.scanning.market_scanner - ERROR - [2m2025-09-16T11:24:04.839653Z[0m [[31m[1merror    [0m] [1mError analyzing market: [<class 'decimal.ConversionSyntax'>][0m [[0m[1m[34msrc.scanning.market_scanner[0m][0m
2025-09-17 23:44:32,911 - src.web3_tools.wallet - ERROR - [2m2025-09-17T15:44:32.911634Z[0m [[31m[1merror    [0m] [1mFailed to get network info: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))[0m [[0m[1m[34msrc.web3_tools.wallet[0m][0m
2025-09-17 23:44:32,917 - src.main - ERROR - [2m2025-09-17T15:44:32.916843Z[0m [[31m[1merror    [0m] [1mLost connection to blockchain network[0m [[0m[1m[34msrc.main[0m][0m
2025-09-18 00:47:23,788 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-18 00:47:28,054 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-18 00:47:29,324 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-18 00:47:34,087 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-18 00:47:35,855 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-18 00:47:40,121 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-18 00:47:42,646 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-18 00:47:47,480 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-18 00:47:51,133 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-18 01:17:21,114 - src.web3_tools.wallet - ERROR - [2m2025-09-17T17:17:21.114172Z[0m [[31m[1merror    [0m] [1mFailed to get network info: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))[0m [[0m[1m[34msrc.web3_tools.wallet[0m][0m
2025-09-18 01:17:21,119 - __main__ - ERROR - [2m2025-09-17T17:17:21.118959Z[0m [[31m[1merror    [0m] [1mLost connection to blockchain network[0m [[0m[1m[34m__main__[0m][0m
2025-09-18 16:50:22,519 - src.telegram_bot.bot - ERROR - [2m2025-09-18T08:50:22.519695Z[0m [[31m[1merror    [0m] [1m初始化組件失敗: 'TelegramHandlers' object has no attribute 'handle_emergency_stop'[0m [[0m[1m[34msrc.telegram_bot.bot[0m][0m
2025-09-18 16:50:22,521 - src.telegram_bot.bot - ERROR - [2m2025-09-18T08:50:22.521683Z[0m [[31m[1merror    [0m] [1m啟動 Bot 失敗: 'TelegramHandlers' object has no attribute 'handle_emergency_stop'[0m [[0m[1m[34msrc.telegram_bot.bot[0m][0m
2025-09-18 16:50:22,523 - __main__ - ERROR - [2m2025-09-18T08:50:22.523438Z[0m [[31m[1merror    [0m] [1m❌ Bot startup failed: 'TelegramHandlers' object has no attribute 'handle_emergency_stop'[0m [[0m[1m[34m__main__[0m][0m
2025-09-18 17:03:41,569 - src.telegram_bot.bot - ERROR - [2m2025-09-18T09:03:41.564382Z[0m [[31m[1merror    [0m] [1mTelegram Bot 錯誤: Failed to get wallet balance: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))[0m [[0m[1m[34msrc.telegram_bot.bot[0m][0m
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
               ^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/urllib3/connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/http/client.py", line 1428, in getresponse
    response.begin()
  File "/usr/lib/python3.12/http/client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/http/client.py", line 300, in _read_status
    raise RemoteDisconnected("Remote end closed connection without"
http.client.RemoteDisconnected: Remote end closed connection without response

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/requests/adapters.py", line 644, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/urllib3/util/retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/urllib3/util/util.py", line 38, in reraise
    raise value.with_traceback(tb)
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
               ^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/urllib3/connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/http/client.py", line 1428, in getresponse
    response.begin()
  File "/usr/lib/python3.12/http/client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/http/client.py", line 300, in _read_status
    raise RemoteDisconnected("Remote end closed connection without"
urllib3.exceptions.ProtocolError: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/root/polymarket_copybot/src/web3_tools/wallet.py", line 105, in get_balance
    matic_wei = self.w3.eth.get_balance(self.trader_account.address)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/web3/eth/eth.py", line 440, in get_balance
    return self._get_balance(account, block_identifier)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/web3/module.py", line 112, in caller
    result = w3.manager.request_blocking(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/web3/manager.py", line 231, in request_blocking
    response = self._make_request(method, params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/web3/manager.py", line 163, in _make_request
    return request_func(method, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/web3/middleware/base.py", line 56, in middleware
    return self.response_processor(method, make_request(method, params))
                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/web3/middleware/base.py", line 56, in middleware
    return self.response_processor(method, make_request(method, params))
                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/web3/middleware/base.py", line 56, in middleware
    return self.response_processor(method, make_request(method, params))
                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 3 more times]
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/web3/_utils/caching/caching_utils.py", line 261, in wrapper
    return func(provider, method, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/web3/providers/rpc/rpc.py", line 169, in make_request
    raw_response = self._make_request(method, request_data)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/web3/providers/rpc/rpc.py", line 146, in _make_request
    return self._request_session_manager.make_post_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/web3/_utils/http_session_manager.py", line 145, in make_post_request
    with self.get_response_from_post_request(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/web3/_utils/http_session_manager.py", line 127, in get_response_from_post_request
    return session.post(endpoint_uri, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/requests/sessions.py", line 637, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/requests/adapters.py", line 659, in send
    raise ConnectionError(err, request=request)
requests.exceptions.ConnectionError: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_application.py", line 1315, in process_update
    await coroutine
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_handlers/basehandler.py", line 159, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/src/telegram_bot/handlers.py", line 114, in start_command
    status = self.bot_controller.get_status()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/src/bot_controller.py", line 191, in get_status
    balances = self.wallet_manager.get_balance()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/src/web3_tools/wallet.py", line 122, in get_balance
    raise Web3Error(f"Failed to get wallet balance: {str(e)}")
src.core.exceptions.Web3Error: Failed to get wallet balance: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-09-18 17:07:58,474 - src.trading.copy_trader - ERROR - [2m2025-09-18T09:07:58.474029Z[0m [[31m[1merror    [0m] [1mError updating monitored wallets: 'Repository' object has no attribute 'get_active_smart_wallets'[0m [[0m[1m[34msrc.trading.copy_trader[0m][0m
2025-09-18 17:07:58,477 - src.scanning.mempool_scanner - ERROR - [2m2025-09-18T09:07:58.477670Z[0m [[31m[1merror    [0m] [1mFailed to load wallets from database: 'Repository' object has no attribute 'get_active_smart_wallets'[0m [[0m[1m[34msrc.scanning.mempool_scanner[0m][0m
2025-09-18 20:59:44,501 - src.web3_tools.wallet - ERROR - [2m2025-09-18T12:59:44.501043Z[0m [[31m[1merror    [0m] [1mFailed to get network info: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))[0m [[0m[1m[34msrc.web3_tools.wallet[0m][0m
2025-09-18 20:59:44,505 - __main__ - ERROR - [2m2025-09-18T12:59:44.505583Z[0m [[31m[1merror    [0m] [1mLost connection to blockchain network[0m [[0m[1m[34m__main__[0m][0m
2025-09-18 21:47:25,775 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-18 21:47:31,346 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-18 21:47:38,373 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-18 21:47:46,620 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-18 21:47:54,144 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-09-18 21:48:03,348 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 122, in network_retry_loop
    await do_action()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_utils/networkloop.py", line 115, in do_action
    action_cb_task.result()
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 676, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 4780, in get_updates
    await self._post(
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 703, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/ext/_extbot.py", line 372, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/_bot.py", line 732, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 198, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/polymarket_copybot/venv/lib/python3.12/site-packages/telegram/request/_baserequest.py", line 375, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
