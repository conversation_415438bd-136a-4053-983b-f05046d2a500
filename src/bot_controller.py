"""Main bot controller for managing trading modes."""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from enum import Enum

from src.core.config import Config
from src.core.exceptions import PolymarketBotError
from src.utils.logging_config import get_logger
from src.database.repository import Repository
from src.web3_tools.wallet import WalletManager
from src.web3_tools.transactions import TransactionManager
from src.trading.polymarket_client import PolymarketClient
from src.trading.copy_trader import CopyTrader, CopyTradeSettings
from src.trading.strategy_trader import StrategyTrader, StrategySettings
from src.scanning.market_scanner import MarketScanner, MarketFilters


class TradingMode(Enum):
    """Available trading modes."""
    COPY_TRADING = "copy_trading"
    STRATEGY_TRADING = "strategy_trading"
    MIXED = "mixed"
    DISABLED = "disabled"


class BotController:
    """Main controller for the Polymarket trading bot."""
    
    def __init__(
        self,
        config: Config,
        repository: Repository,
        wallet_manager: <PERSON>et<PERSON>ana<PERSON>,
        transaction_manager: TransactionManager,
        polymarket_client: PolymarketClient
    ):
        """Initialize bot controller.
        
        Args:
            config: Bot configuration
            repository: Database repository
            wallet_manager: Wallet manager
            transaction_manager: Transaction manager
            polymarket_client: Polymarket client
        """
        self.config = config
        self.repository = repository
        self.wallet_manager = wallet_manager
        self.transaction_manager = transaction_manager
        self.polymarket_client = polymarket_client
        self.logger = get_logger(__name__)
        
        # Trading components
        self.copy_trader: Optional[CopyTrader] = None
        self.strategy_trader: Optional[StrategyTrader] = None
        self.market_scanner: Optional[MarketScanner] = None
        
        # Current mode
        self.current_mode = TradingMode.DISABLED
        self.is_running = False
        
        # Settings
        self.copy_settings = CopyTradeSettings()
        self.strategy_settings = StrategySettings()
        self.market_filters = MarketFilters()
        
        self.logger.info("BotController initialized")
    
    async def initialize_components(self) -> None:
        """Initialize all trading components."""
        try:
            self.logger.info("Initializing trading components...")
            
            # Initialize market scanner
            self.market_scanner = MarketScanner(self.config, self.repository)
            
            # Initialize copy trader
            self.copy_trader = CopyTrader(
                config=self.config,
                repository=self.repository,
                wallet_manager=self.wallet_manager,
                polymarket_client=self.polymarket_client,
                copy_settings=self.copy_settings
            )
            await self.copy_trader.initialize_components()
            
            # Initialize strategy trader
            self.strategy_trader = StrategyTrader(
                config=self.config,
                repository=self.repository,
                wallet_manager=self.wallet_manager,
                polymarket_client=self.polymarket_client,
                market_scanner=self.market_scanner,
                settings=self.strategy_settings
            )
            
            self.logger.info("✅ All trading components initialized")
            
        except Exception as e:
            raise PolymarketBotError(f"Failed to initialize components: {e}")
    
    async def start_trading(self, mode: TradingMode) -> None:
        """Start trading in specified mode.
        
        Args:
            mode: Trading mode to start
        """
        if self.is_running:
            await self.stop_trading()
        
        self.current_mode = mode
        self.is_running = True
        
        self.logger.info(f"🚀 Starting trading in {mode.value} mode")
        
        try:
            if mode == TradingMode.COPY_TRADING:
                await self._start_copy_trading()
            elif mode == TradingMode.STRATEGY_TRADING:
                await self._start_strategy_trading()
            elif mode == TradingMode.MIXED:
                await self._start_mixed_trading()
            else:
                self.logger.warning(f"Invalid trading mode: {mode}")
                self.is_running = False
                
        except Exception as e:
            self.logger.error(f"Error starting trading: {e}")
            self.is_running = False
            raise
    
    async def stop_trading(self) -> None:
        """Stop all trading activities."""
        if not self.is_running:
            return
        
        self.logger.info("🛑 Stopping all trading activities...")
        
        try:
            # Stop copy trader
            if self.copy_trader:
                await self.copy_trader.stop_copy_trading()
            
            # Stop strategy trader
            if self.strategy_trader:
                await self.strategy_trader.stop_trading()
            
            self.is_running = False
            self.current_mode = TradingMode.DISABLED
            
            self.logger.info("✅ All trading activities stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping trading: {e}")
    
    async def _start_copy_trading(self) -> None:
        """Start copy trading mode."""
        if not self.copy_trader:
            raise PolymarketBotError("Copy trader not initialized")
        
        await self.copy_trader.start_copy_trading()
    
    async def _start_strategy_trading(self) -> None:
        """Start strategy trading mode."""
        if not self.strategy_trader:
            raise PolymarketBotError("Strategy trader not initialized")
        
        await self.strategy_trader.start_trading()
    
    async def _start_mixed_trading(self) -> None:
        """Start both copy and strategy trading simultaneously."""
        if not self.copy_trader or not self.strategy_trader:
            raise PolymarketBotError("Trading components not initialized")
        
        # Start both trading modes concurrently
        await asyncio.gather(
            self.copy_trader.start_copy_trading(),
            self.strategy_trader.start_trading(),
            return_exceptions=True
        )
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive bot status.
        
        Returns:
            Status dictionary
        """
        # Get wallet balances
        balances = self.wallet_manager.get_balance()
        
        # Get component performance stats
        copy_stats = {}
        strategy_stats = {}
        
        if self.copy_trader:
            copy_stats = self.copy_trader.get_performance_stats()
        
        if self.strategy_trader:
            strategy_stats = self.strategy_trader.get_performance_stats()
        
        # Get market scanner stats
        scanner_stats = {}
        if self.market_scanner:
            scanner_stats = self.market_scanner.get_scanning_stats()
        
        return {
            'is_running': self.is_running,
            'current_mode': self.current_mode.value,
            'wallet_address': self.wallet_manager.get_trader_address(),
            'balances': {
                'USDC': float(balances['USDC']),
                'MATIC': float(balances['MATIC'])
            },
            'copy_trading': copy_stats,
            'strategy_trading': strategy_stats,
            'market_scanner': scanner_stats,
            'settings': {
                'copy_settings': {
                    'max_trade_amount': float(self.copy_settings.max_trade_amount),
                    'copy_ratio': float(self.copy_settings.copy_ratio),
                    'max_slippage': float(self.copy_settings.max_slippage),
                    'max_daily_volume': float(self.copy_settings.max_daily_volume)
                },
                'strategy_settings': {
                    'max_trade_amount': float(self.strategy_settings.max_trade_amount),
                    'min_confidence_score': float(self.strategy_settings.min_confidence_score),
                    'max_daily_trades': self.strategy_settings.max_daily_trades,
                    'max_positions': self.strategy_settings.max_positions
                }
            }
        }
    
    def update_copy_settings(self, **kwargs) -> None:
        """Update copy trading settings.
        
        Args:
            **kwargs: Settings to update
        """
        for key, value in kwargs.items():
            if hasattr(self.copy_settings, key):
                setattr(self.copy_settings, key, value)
        
        if self.copy_trader:
            self.copy_trader.update_settings(self.copy_settings)
        
        self.logger.info("Copy trading settings updated", **kwargs)
    
    def update_strategy_settings(self, **kwargs) -> None:
        """Update strategy trading settings.
        
        Args:
            **kwargs: Settings to update
        """
        for key, value in kwargs.items():
            if hasattr(self.strategy_settings, key):
                setattr(self.strategy_settings, key, value)
        
        self.logger.info("Strategy trading settings updated", **kwargs)
    
    def update_market_filters(self, **kwargs) -> None:
        """Update market scanning filters.
        
        Args:
            **kwargs: Filters to update
        """
        for key, value in kwargs.items():
            if hasattr(self.market_filters, key):
                setattr(self.market_filters, key, value)
        
        if self.market_scanner:
            self.market_scanner.update_filters(self.market_filters)

        self.logger.info("Market scanning filters updated", **kwargs)

    async def emergency_stop(self) -> None:
        """Execute emergency stop for current trading mode."""
        try:
            self.logger.warning("Emergency stop initiated")

            # Stop current trading activities
            if self.current_mode == TradingMode.V2_COPY:
                if self.copy_trader:
                    await self.copy_trader.stop()
            elif self.current_mode == TradingMode.V1_STRATEGY:
                if self.strategy_trader:
                    await self.strategy_trader.stop()
            elif self.current_mode == TradingMode.HYBRID:
                if self.copy_trader:
                    await self.copy_trader.stop()
                if self.strategy_trader:
                    await self.strategy_trader.stop()

            # Stop market scanning
            if self.market_scanner:
                await self.market_scanner.stop()

            self.is_running = False
            self.logger.warning("Emergency stop completed")

        except Exception as e:
            self.logger.error(f"Emergency stop failed: {e}")
            raise

    async def emergency_stop_all(self) -> None:
        """Execute emergency stop for all systems."""
        try:
            self.logger.critical("Emergency stop all initiated")

            # Stop all trading components
            if self.copy_trader:
                await self.copy_trader.stop()
            if self.strategy_trader:
                await self.strategy_trader.stop()
            if self.market_scanner:
                await self.market_scanner.stop()

            # Set mode to disabled
            self.current_mode = TradingMode.DISABLED
            self.is_running = False

            self.logger.critical("Emergency stop all completed")

        except Exception as e:
            self.logger.error(f"Emergency stop all failed: {e}")
            raise

    """Main bot controller for managing trading modes."""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from enum import Enum

from src.core.config import Config
from src.core.exceptions import PolymarketBotError
from src.utils.logging_config import get_logger
from src.database.repository import Repository
from src.web3_tools.wallet import WalletManager
from src.web3_tools.transactions import TransactionManager
from src.trading.polymarket_client import PolymarketClient
from src.trading.copy_trader import CopyTrader, CopyTradeSettings
from src.trading.strategy_trader import StrategyTrader, StrategySettings
from src.scanning.market_scanner import MarketScanner, MarketFilters
from src.telegram_bot.notifications import NotificationManager


class TradingMode(Enum):
    """Available trading modes."""
    COPY_TRADING = "copy_trading"
    STRATEGY_TRADING = "strategy_trading"
    MIXED = "mixed"
    DISABLED = "disabled"


class BotController:
    """Main controller for the Polymarket trading bot."""
    
    def __init__(
        self,
        config: Config,
        repository: Repository,
        wallet_manager: WalletManager,
        transaction_manager: TransactionManager,
        polymarket_client: PolymarketClient
    ):
        """Initialize bot controller.
        
        Args:
            config: Bot configuration
            repository: Database repository
            wallet_manager: Wallet manager
            transaction_manager: Transaction manager
            polymarket_client: Polymarket client
        """
        self.config = config
        self.repository = repository
        self.wallet_manager = wallet_manager
        self.transaction_manager = transaction_manager
        self.polymarket_client = polymarket_client
        self.logger = get_logger(__name__)
        
        # Notification manager
        self.notification_manager = NotificationManager(config)

        # Trading components
        self.copy_trader: Optional[CopyTrader] = None
        self.strategy_trader: Optional[StrategyTrader] = None
        self.market_scanner: Optional[MarketScanner] = None
        
        # Current mode and user
        self.current_mode = TradingMode.DISABLED
        self.is_running = False
        self.active_user_id: Optional[int] = None
        
        # Settings
        self.copy_settings = CopyTradeSettings()
        self.strategy_settings = StrategySettings()
        self.market_filters = MarketFilters()
        
        self.logger.info("BotController initialized")
    
    async def initialize_components(self) -> None:
        """Initialize all trading components."""
        try:
            self.logger.info("Initializing trading components...")
            
            # Initialize market scanner
            self.market_scanner = MarketScanner(self.config, self.repository)
            
            # Initialize copy trader
            self.copy_trader = CopyTrader(
                config=self.config,
                repository=self.repository,
                wallet_manager=self.wallet_manager,
                polymarket_client=self.polymarket_client,
                copy_settings=self.copy_settings
            )
            await self.copy_trader.initialize_components()
            
            # Initialize strategy trader
            self.strategy_trader = StrategyTrader(
                config=self.config,
                repository=self.repository,
                wallet_manager=self.wallet_manager,
                polymarket_client=self.polymarket_client,
                market_scanner=self.market_scanner,
                settings=self.strategy_settings,
                notification_manager=self.notification_manager
            )
            
            self.logger.info("✅ All trading components initialized")
            
        except Exception as e:
            raise PolymarketBotError(f"Failed to initialize components: {e}")
    
    async def start_trading(self, mode: TradingMode, user_id: int) -> None:
        """Start trading in specified mode for a given user.

        Args:
            mode: Trading mode to start
            user_id: The ID of the user initiating the trade
        """
        if self.is_running:
            await self.stop_trading()
        
        self.current_mode = mode
        self.is_running = True
        self.active_user_id = user_id
        
        self.logger.info(f"🚀 User {user_id} starting trading in {mode.value} mode")
        
        try:
            if mode == TradingMode.COPY_TRADING:
                await self._start_copy_trading(user_id)
            elif mode == TradingMode.STRATEGY_TRADING:
                await self._start_strategy_trading(user_id)
            elif mode == TradingMode.MIXED:
                await self._start_mixed_trading(user_id)
            else:
                self.logger.warning(f"Invalid trading mode: {mode}")
                self.is_running = False
                
        except Exception as e:
            self.logger.error(f"Error starting trading for user {user_id}: {e}")
            self.is_running = False
            if self.notification_manager:
                await self.notification_manager.send_trade_failure_notification(user_id, {'error': str(e), 'market_title': 'System Startup'})
            raise
    
    async def stop_trading(self) -> None:
        """Stop all trading activities."""
        if not self.is_running:
            return
        
        self.logger.info("🛑 Stopping all trading activities...")
        
        try:
            # Stop copy trader
            if self.copy_trader:
                await self.copy_trader.stop_copy_trading()
            
            # Stop strategy trader
            if self.strategy_trader:
                await self.strategy_trader.stop_trading()
            
            self.is_running = False
            self.current_mode = TradingMode.DISABLED
            self.active_user_id = None
            
            self.logger.info("✅ All trading activities stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping trading: {e}")
    
    async def _start_copy_trading(self, user_id: int) -> None:
        """Start copy trading mode."""
        if not self.copy_trader:
            raise PolymarketBotError("Copy trader not initialized")
        
        # This assumes copy_trader.start_copy_trading is updated to accept user_id
        await self.copy_trader.start_copy_trading(user_id)
    
    async def _start_strategy_trading(self, user_id: int) -> None:
        """Start strategy trading mode."""
        if not self.strategy_trader:
            raise PolymarketBotError("Strategy trader not initialized")
        
        await self.strategy_trader.start_trading(user_id)
    
    async def _start_mixed_trading(self, user_id: int) -> None:
        """Start both copy and strategy trading simultaneously."""
        if not self.copy_trader or not self.strategy_trader:
            raise PolymarketBotError("Trading components not initialized")
        
        # Start both trading modes concurrently
        await asyncio.gather(
            self.copy_trader.start_copy_trading(user_id),
            self.strategy_trader.start_trading(user_id),
            return_exceptions=True
        )
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive bot status.
        
        Returns:
            Status dictionary
        """
        # Get wallet balances
        balances = self.wallet_manager.get_balance()
        
        # Get component performance stats
        copy_stats = {}
        strategy_stats = {}
        
        if self.copy_trader:
            copy_stats = self.copy_trader.get_performance_stats()
        
        if self.strategy_trader:
            strategy_stats = self.strategy_trader.get_performance_stats()
        
        # Get market scanner stats
        scanner_stats = {}
        if self.market_scanner:
            scanner_stats = self.market_scanner.get_scanning_stats()
        
        return {
            'is_running': self.is_running,
            'current_mode': self.current_mode.value,
            'wallet_address': self.wallet_manager.get_trader_address(),
            'balances': {
                'USDC': float(balances['USDC']),
                'MATIC': float(balances['MATIC'])
            },
            'copy_trading': copy_stats,
            'strategy_trading': strategy_stats,
            'market_scanner': scanner_stats,
            'settings': {
                'copy_settings': {
                    'max_trade_amount': float(self.copy_settings.max_trade_amount),
                    'copy_ratio': float(self.copy_settings.copy_ratio),
                    'max_slippage': float(self.copy_settings.max_slippage),
                    'max_daily_volume': float(self.copy_settings.max_daily_volume)
                },
                'strategy_settings': {
                    'max_trade_amount': float(self.strategy_settings.max_trade_amount),
                    'min_confidence_score': float(self.strategy_settings.min_confidence_score),
                    'max_daily_trades': self.strategy_settings.max_daily_trades,
                    'max_positions': self.strategy_settings.max_positions
                }
            }
        }
    
    def update_copy_settings(self, **kwargs) -> None:
        """Update copy trading settings.
        
        Args:
            **kwargs: Settings to update
        """
        for key, value in kwargs.items():
            if hasattr(self.copy_settings, key):
                setattr(self.copy_settings, key, value)
        
        if self.copy_trader:
            self.copy_trader.update_settings(self.copy_settings)
        
        self.logger.info("Copy trading settings updated", **kwargs)
    
    def update_strategy_settings(self, **kwargs) -> None:
        """Update strategy trading settings.
        
        Args:
            **kwargs: Settings to update
        """
        for key, value in kwargs.items():
            if hasattr(self.strategy_settings, key):
                setattr(self.strategy_settings, key, value)
        
        self.logger.info("Strategy trading settings updated", **kwargs)
    
    def update_market_filters(self, **kwargs) -> None:
        """Update market scanning filters.
        
        Args:
            **kwargs: Filters to update
        """
        for key, value in kwargs.items():
            if hasattr(self.market_filters, key):
                setattr(self.market_filters, key, value)
        
        if self.market_scanner:
            self.market_scanner.update_filters(self.market_filters)

        self.logger.info("Market scanning filters updated", **kwargs)

    async def emergency_stop(self) -> None:
        """Execute emergency stop for current trading mode."""
        try:
            self.logger.warning("Emergency stop initiated")

            # Stop current trading activities
            if self.current_mode == TradingMode.V2_COPY:
                if self.copy_trader:
                    await self.copy_trader.stop()
            elif self.current_mode == TradingMode.V1_STRATEGY:
                if self.strategy_trader:
                    await self.strategy_trader.stop()
            elif self.current_mode == TradingMode.HYBRID:
                if self.copy_trader:
                    await self.copy_trader.stop()
                if self.strategy_trader:
                    await self.strategy_trader.stop()

            # Stop market scanning
            if self.market_scanner:
                await self.market_scanner.stop()

            self.is_running = False
            self.logger.warning("Emergency stop completed")

        except Exception as e:
            self.logger.error(f"Emergency stop failed: {e}")
            raise

    async def emergency_stop_all(self) -> None:
        """Execute emergency stop for all systems."""
        try:
            self.logger.critical("Emergency stop all initiated")

            # Stop all trading components
            if self.copy_trader:
                await self.copy_trader.stop()
            if self.strategy_trader:
                await self.strategy_trader.stop()
            if self.market_scanner:
                await self.market_scanner.stop()

            # Set mode to disabled
            self.current_mode = TradingMode.DISABLED
            self.is_running = False

            self.logger.critical("Emergency stop all completed")

        except Exception as e:
            self.logger.error(f"Emergency stop all failed: {e}")
            raise

    async def activate_risk_protection(self) -> dict:
        """Activate emergency risk protection mode."""
        try:
            self.logger.warning("Activating emergency risk protection")

            # Reduce trading limits
            original_copy_amount = self.copy_settings.max_trade_amount
            original_strategy_amount = self.strategy_settings.max_trade_amount

            # Set to 10% of original amounts
            self.copy_settings.max_trade_amount = original_copy_amount * 0.1
            self.strategy_settings.max_trade_amount = original_strategy_amount * 0.1

            # Update components with new settings
            if self.copy_trader:
                self.copy_trader.update_settings(self.copy_settings)

            protection_status = {
                'level': '高',
                'copy_limit_reduced': f"{original_copy_amount} -> {self.copy_settings.max_trade_amount}",
                'strategy_limit_reduced': f"{original_strategy_amount} -> {self.strategy_settings.max_trade_amount}",
                'timestamp': datetime.now().isoformat()
            }

            self.logger.warning("Emergency risk protection activated", **protection_status)
            return protection_status

        except Exception as e:
            self.logger.error(f"Risk protection activation failed: {e}")
            raise
