#!/usr/bin/env python3
"""
Telegram Bot 命令处理器模組
處理所有的 Telegram Bot 命令和用戶交互
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from telegram import Update, InlineKeyboardMarkup
from telegram.ext import ContextTypes, CommandHandler, CallbackQueryHandler, MessageHandler, filters
from telegram.constants import ParseMode

from src.core.config import Config
from src.bot_controller import BotController, TradingMode
from src.utils.logging_config import get_logger
from .session_manager import SessionManager


class TelegramHandlers:
    """Telegram Bot 命令处理器類"""
    
    def __init__(self, config: Config, bot_controller: BotController, session_manager: SessionManager):
        """初始化處理器
        
        Args:
            config: 系統配置
            bot_controller: 機器人控制器
            session_manager: 會話管理器
        """
        self.config = config
        self.bot_controller = bot_controller
        self.session_manager = session_manager
        self.logger = get_logger(__name__)
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理 /start 命令，兼容命令和回調"""
        if update.callback_query:
            await update.callback_query.answer()
            
        user_id = update.effective_user.id
        username = update.effective_user.username or "未知用戶"
        chat_id = update.effective_chat.id
        
        # 獲取或創建用戶會話
        session = self.session_manager.get_or_create_session(user_id, username, chat_id)
        
        self.logger.info(f"用戶 {username} ({user_id}) 啟動 Bot，授權狀態: {session.is_authorized}")
        
        # 檢查授權狀態
        if not session.is_authorized:
            if self.session_manager.is_auto_authorization_enabled():
                await self._show_auto_authorization_flow(update, user_id, username)
            else:
                await self._show_manual_authorization_request(update, user_id, username)
            return
        
        # 用戶已授權，確保用戶擁有專屬錢包
        wallet_created = self.session_manager.ensure_user_wallet(user_id, username)
        
        if wallet_created:
            wallet_address = self.session_manager.get_user_wallet_address(user_id)
            welcome_message = f"""👋 歡迎回來，{username}！

這是您的 Polymarket AI 交易助手。
請選擇您要使用的功能：

📈 **策略機器人** - 運行您預設的高勝率策略。
� **跟單機器人** - 複製頂尖交易員的操作。

👇 您也可以管理您的資金和查看績效。

💰 **您的專屬錢包地址：**
`{wallet_address}`
*(點擊可複製地址)*"""
        else:
            welcome_message = f"""👋 歡迎回來，{username}！

❌ **錢包創建失敗**
無法為您創建專屬錢包，請聯繫管理員。"""
        
        from .keyboards import BotKeyboards
        
        status = self.bot_controller.get_status()
        current_mode = self._format_trading_mode_short(status['current_mode'])
        is_running = status['is_running']
        
        user_balances = self.session_manager.get_user_balance(user_id)
        has_balance = user_balances.get('USDC', 0) > 10
        
        keyboard = BotKeyboards.get_main_menu(
            is_running=is_running,
            current_mode=current_mode,
            has_balance=has_balance
        )
        
        # 使用 effective_message 兼容命令和回調
        if update.effective_message:
            await update.effective_message.reply_text(
                welcome_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理 /status 命令"""
        user_id = update.effective_user.id
        
        if not self._check_user_authorization(user_id):
            await update.effective_message.reply_text("❌ 未授權用戶")
            return
        
        try:
            # 確保用戶有錢包
            username = update.effective_user.username or "未知用戶"
            wallet_created = self.session_manager.ensure_user_wallet(user_id, username)
            
            if not wallet_created:
                await update.effective_message.reply_text("❌ 無法獲取您的專屬錢包，請聯繫管理員")
                return
            
            # 獲取系統狀態
            status = self.bot_controller.get_status()
            
            # 獲取用戶專屬錢包信息
            wallet_address = self.session_manager.get_user_wallet_address(user_id)
            user_balances = self.session_manager.get_user_balance(user_id)
            
            status_message = f"""
📊 **您的個人狀態報告**

**🔒 錢包安全狀態：**
✅ 專屬錢包已創建
✅ 私鑰安全加密存儲
✅ 與其他用戶完全隔離

**💰 您的專屬錢包：**
📍 錢包地址：`{wallet_address[:10]}...{wallet_address[-8:]}`
💵 USDC 餘額：${user_balances.get('USDC', 0):.2f}
⛽ MATIC 餘額：{user_balances.get('MATIC', 0):.4f}

**🤖 系統狀態：**
🟢 系統狀態：{'🟢 運行中' if status['is_running'] else '🔴 已停止'}
🎯 當前模式：{self._format_trading_mode(status['current_mode'])}
⏰ 運行時間：{status.get('uptime', '未知')}

**📈 您的交易統計：**
💡 提示：交易記錄完全獨立，僅您可見
📊 總交易數：{status.get('total_trades', 0)}
✅ 成功交易：{status.get('successful_trades', 0)}
❌ 失敗交易：{status.get('failed_trades', 0)}
💼 當前持倉：{status.get('current_positions', 0)}

**🎯 您的性能指標：**
📈 總 PnL：${status.get('total_pnl', 0):.2f}
📊 勝率：{status.get('win_rate', 0):.1f}%
⚡ 平均延遲：{status.get('avg_latency', 0):.2f}s

**🛡️ 隱私保護：**
• 您的交易數據完全私密
• 其他用戶無法查看您的餘額或交易
• 錢包私鑰經過高強度加密保護
"""
            
            from .keyboards import BotKeyboards
            keyboard = BotKeyboards.get_main_menu(
                is_running=self.bot_controller.is_running,
                current_mode=self._format_trading_mode(self.bot_controller.current_mode),
                has_balance=user_balances.get('USDC', 0) > 10  # 假設需要至少$10才算有餘額
            )
            
            await update.effective_message.reply_text(
                status_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            self.logger.error(f"獲取狀態失敗: {e}")
            await update.effective_message.reply_text(f"❌ 獲取狀態失敗: {str(e)}")
    
    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理 /balance 命令"""
        user_id = update.effective_user.id
        
        if not self._check_user_authorization(user_id):
            await update.effective_message.reply_text("❌ 未授權用戶")
            return
        
        try:
            # 確保用戶有錢包
            username = update.effective_user.username or "未知用戶"
            wallet_created = self.session_manager.ensure_user_wallet(user_id, username)
            
            if not wallet_created:
                await update.effective_message.reply_text("❌ 無法獲取您的專屬錢包，請聯繫管理員")
                return
            
            # 獲取用戶專屬錢包信息
            wallet_address = self.session_manager.get_user_wallet_address(user_id)
            balances = self.session_manager.get_user_balance(user_id)
            
            if 'error' in balances:
                await update.effective_message.reply_text(f"❌ 獲取餘額失敗: {balances['error']}")
                return
            
            balance_message = f"""
💰 **您的專屬錢包餘額報告**

**🔒 您的專屬錢包地址：**
`{wallet_address}`

**代幣餘額：**
💵 USDC：{balances.get('USDC', 0):.6f}
⛽ MATIC：{balances.get('MATIC', 0):.6f}

**🛡️ 安全狀態：**
✅ 錢包完全獨立，其他用戶無法訪問
✅ 私鑰加密存儲，安全可靠
✅ 與其他用戶資金完全隔離

**💰 充值指導：**
• 網路：Polygon Mainnet
• 建議充值：0.1-0.5 MATIC (Gas 費) + $10-50 USDC (交易)
• Polygon USDC 合約: ******************************************

**⚠️ 安全提醒：**
• 這是您的專屬交易錢包，與其他用戶完全隔離
• 建議先小額測試，熟悉系統後再增加資金
• 請勿將大量資金存入機器人錢包
• 交易前請確保有足夠的 MATIC 支付 Gas 費用

**建議操作：**
• 如需交易，請確保 USDC 餘額 > $10
• 如需交易，請確保 MATIC 餘額 > 0.1
• 定期備份和檢查錢包安全
"""
            
            await update.effective_message.reply_text(
                balance_message,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            self.logger.error(f"獲取餘額失敗: {e}")
            await update.effective_message.reply_text(f"❌ 獲取餘額失敗: {str(e)}")
    
    async def mode_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理 /mode 命令 - 交易模式切換"""
        user_id = update.effective_user.id
        
        if not self._check_user_authorization(user_id):
            await update.effective_message.reply_text("❌ 未授權用戶")
            return
        
        current_mode = self.bot_controller.current_mode
        
        mode_message = f"""
🎯 **交易模式管理**

**當前模式：** {self._format_trading_mode(current_mode)}
**運行狀態：** {'🟢 運行中' if self.bot_controller.is_running else '🔴 已停止'}

**可用模式：**

🎯 **V2 跟單模式**
跟隨聰明錢錢包的交易，自動復制高勝率玩家的策略

📈 **V1 策略模式**  
基於預設條件自主分析市場，執行策略交易

🔀 **混合模式**
同時運行跟單和策略模式，最大化交易機會

🛑 **停用模式**
停止所有自動交易功能

選擇要切換的模式：
"""
        
        from .keyboards import BotKeyboards
        
        # 獲取系統狀態信息
        status = self.bot_controller.get_status()
        current_mode = self._format_trading_mode_short(status['current_mode'])
        is_running = status['is_running']
        
        keyboard = BotKeyboards.get_trading_mode_menu(
            current_mode=current_mode,
            is_running=is_running
        )
        
        await update.effective_message.reply_text(
            mode_message,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def handle_mode_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理模式選擇回調, 新增確認步驟"""
        query = update.callback_query
        await query.answer()

        user_id = query.from_user.id
        mode_str = query.data.replace('mode_', '')
        self.logger.info(f"用戶 {user_id} 準備選擇模式 '{mode_str}'")

        from .keyboards import BotKeyboards

        try:
            mode_map = {
                'copy': TradingMode.COPY_TRADING,
                'strategy': TradingMode.STRATEGY_TRADING,
                'mixed': TradingMode.MIXED,
                'disabled': TradingMode.DISABLED
            }
            new_mode = mode_map.get(mode_str)

            if new_mode is None:
                self.logger.warning(f"用戶 {user_id} 選擇了無效模式: {mode_str}")
                await query.edit_message_text("❌ 無效的模式選擇")
                return

            # 如果是停止模式，直接執行
            if new_mode == TradingMode.DISABLED:
                self.logger.info(f"用戶 {user_id} 正在停止交易...")
                await self.bot_controller.stop_trading()
                self.logger.info(f"用戶 {user_id} 的交易已停止。")
                await query.edit_message_text(
                    f"✅ 已切換到：{self._format_trading_mode(new_mode)}\n🛑 所有交易已停止"
                )
                await self._send_mode_change_notification(user_id, new_mode)
                # 顯示更新後的模式菜單
                await self.handle_mode_menu(update, context)
                return

            # 對於啟動交易的模式，顯示確認畫面
            user_balances = self.session_manager.get_user_balance(user_id)
            usdc_balance = user_balances.get('USDC', 0)
            matic_balance = user_balances.get('MATIC', 0)

            # 檢查是否有足夠餘額進行交易
            has_sufficient_usdc = usdc_balance >= 10
            has_sufficient_matic = matic_balance >= 0.1
            can_trade = has_sufficient_usdc and has_sufficient_matic

            if not can_trade:
                # 餘額嚴重不足，阻止啟動並提供充值指導
                wallet_address = self.session_manager.get_user_wallet_address(user_id)

                insufficient_balance_message = f"""
❌ **無法啟動 {self._format_trading_mode(new_mode)}**

**餘額不足：**
💵 USDC: ${usdc_balance:.2f} {'✅' if has_sufficient_usdc else '❌ (需要 > $50)'}
⛽ POL(原MATIC): {matic_balance:.4f} {'✅' if has_sufficient_matic else '❌ (需要 > 10)'}

**您的錢包地址：**
`{wallet_address}`

**充值建議：**
• 交易最低要求: USDC > 50, POL > 10
• 建議要求: USDC > 100, POL > 20

**充值後請重新嘗試啟動交易模式。**
"""

                from .keyboards import BotKeyboards
                error_keyboard = BotKeyboards.get_post_action_keyboard("error")

                await query.edit_message_text(
                    insufficient_balance_message,
                    reply_markup=error_keyboard,
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # 餘額足夠，顯示確認畫面
            balance_status = ""
            if usdc_balance < 20:
                balance_status += "\n💡 **建議**: 考慮增加 USDC 餘額以獲得更多交易機會"
            if matic_balance < 0.5:
                balance_status += "\n💡 **建議**: 考慮增加 MATIC 餘額以確保 Gas 費用充足"

            confirmation_message = f"""
🚀 **準備啟動 {self._format_trading_mode(new_mode)}**

**當前餘額:**
💵 USDC: ${usdc_balance:.2f} ✅
⛽ MATIC: {matic_balance:.4f} ✅
{balance_status}

**模式說明:**
{self._get_mode_description(new_mode)}

**確認啟動此模式嗎？**
"""

            keyboard = BotKeyboards.get_confirmation_keyboard(action=f"start_{mode_str}")
            await query.edit_message_text(
                confirmation_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"模式選擇過程中斷: {e}", exc_info=True)
            await query.edit_message_text(f"❌ 處理模式選擇失敗: {str(e)}")

    async def handle_confirmation(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理所有確認和取消的回調"""
        query = update.callback_query
        await query.answer()

        user_id = query.from_user.id
        callback_data = query.data

        self.logger.info(f"用戶 {user_id} 進行確認操作: {callback_data}")

        try:
            parts = callback_data.split('_')
            action_type, action_name = parts[0], '_'.join(parts[1:])

            if action_type == "cancel":
                self.logger.info(f"用戶 {user_id} 取消了操作: {action_name}")

                # 根據取消的操作類型提供不同的返回邏輯
                if action_name.startswith("start_"):
                    # 取消啟動操作，返回模式選擇菜單
                    await query.edit_message_text(
                        "❌ **操作已取消**\n\n"
                        "您已取消啟動交易模式。\n"
                        "請選擇其他操作或重新選擇模式。",
                        parse_mode=ParseMode.MARKDOWN
                    )
                    # 短暫延遲後顯示模式菜單
                    await asyncio.sleep(1)
                    await self.handle_mode_menu(update, context)
                else:
                    # 其他取消操作，返回主選單
                    from .keyboards import BotKeyboards
                    main_keyboard = BotKeyboards.get_main_menu()

                    await query.edit_message_text(
                        "❌ **操作已取消**\n\n"
                        "請選擇其他操作。",
                        reply_markup=main_keyboard,
                        parse_mode=ParseMode.MARKDOWN
                    )
                return

            if action_type == "confirm":
                if action_name.startswith("start_"):
                    mode_str = action_name.replace("start_", "")
                    mode_map = {
                        'copy': TradingMode.COPY_TRADING,
                        'strategy': TradingMode.STRATEGY_TRADING,
                        'mixed': TradingMode.MIXED,
                    }
                    new_mode = mode_map.get(mode_str)

                    if not new_mode:
                        await query.edit_message_text("❌ 無效的確認操作。 সন")
                        return
                        
                    self.logger.info(f"用戶 {user_id} 確認啟動模式: {new_mode.value}")
                    await query.edit_message_text(f"⏳ 正在啟動 {self._format_trading_mode(new_mode)}，請稍候...")

                    # 異步啟動交易模式
                    asyncio.create_task(self._start_trading_with_updates(query, new_mode, user_id))
                    return
            
            # 如果有其他類型的確認，可以在此處擴展
            await query.edit_message_text("❓ 未知的確認操作。 সন")

        except Exception as e:
            self.logger.error(f"確認操作處理失敗: {e}", exc_info=True)
            await query.edit_message_text(f"❌ 確認操作失敗: {str(e)}")
    
    async def handle_mode_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """处理模式菜单"""
        query = update.callback_query
        await query.answer()
        
        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授权用户")
            return
        
        # 直接通过回调编辑消息展示模式菜单，避免在回调上下文中访问 update.message 为 None
        try:
            # 文案与 /mode 命令保持一致
            mode_message = f"""
🎯 **交易模式管理**

**當前模式：** {self._format_trading_mode(self.bot_controller.current_mode)}
**運行狀態：** {'🟢 運行中' if self.bot_controller.is_running else '🔴 已停止'}

**可用模式：**

🎯 **V2 跟單模式**
跟隨聰明錢錢包的交易，自動復制高勝率玩家的策略

📈 **V1 策略模式**  
基於預設條件自主分析市場，執行策略交易

🔀 **混合模式**
同時運行跟單和策略模式，最大化交易機會

🛑 **停用模式**
停止所有自動交易功能

選擇要切換的模式：
"""
            
            from .keyboards import BotKeyboards
            status = self.bot_controller.get_status()
            current_mode_short = self._format_trading_mode_short(status['current_mode'])
            is_running = status['is_running']
            keyboard = BotKeyboards.get_trading_mode_menu(
                current_mode=current_mode_short,
                is_running=is_running
            )
            
            await query.edit_message_text(
                mode_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            self.logger.error(f"显示模式菜单失败: {e}")
            await query.edit_message_text(f"❌ 显示模式菜单失败: {str(e)}")
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理 /help 命令"""
        help_message = """
❓ **幫助文檔**

**基本命令：**
• `/start` - 開始使用 Bot
• `/status` - 查看系統狀態
• `/balance` - 查看錢包餘額
• `/mode` - 切換交易模式
• `/help` - 查看此幫助

**交易模式說明：**

**🎯 V2 跟單模式：**
• 監控 Polygon Mempool 中的聰明錢交易
• 自動跟隨高勝率玩家的策略
• 實時解析和復制交易

**📈 V1 策略模式：**
• 掃描即將到期的高概率市場
• 基於預設條件自動下單
• 智能風險管理

**🔀 混合模式：**
• 同時運行兩種模式
• 最大化交易機會
• 統一風險控制

**安全須知：**
⚠️ 請使用專用測試錢包
⚠️ 從小額開始測試
⚠️ 理解所有風險
⚠️ 遵循當地法規

**技術支持：**
如有問題請查看日誌或聯繫開發團隊
"""
        
        await update.message.reply_text(
            help_message,
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def handle_dashboard_main(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理智能控制台主頁面"""
        query = update.callback_query
        await query.answer()
        
        user_id = update.effective_user.id
        username = update.effective_user.username or "未知用戶"
        
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return
        
        try:
            # 確保用戶有錢包
            wallet_created = self.session_manager.ensure_user_wallet(user_id, username)
            if not wallet_created:
                await query.edit_message_text("❌ 無法獲取您的專屬錢包，請聯繫管理員")
                return
            
            # 獲取系統狀態和用戶信息
            status = self.bot_controller.get_status()
            wallet_address = self.session_manager.get_user_wallet_address(user_id)
            user_balances = self.session_manager.get_user_balance(user_id)
            
            # 生成控制台信息
            dashboard_message = f"""
🏠 **您的智能控制台**

💰 **專屬錢包餘額**
💵 USDC: ${user_balances.get('USDC', 0):.2f}
⛽ MATIC: {user_balances.get('MATIC', 0):.4f}

🤖 **系統運行狀態**
{'🟢' if status['is_running'] else '🔴'} 狀態: {'運行中' if status['is_running'] else '已停止'}
🎯 模式: {self._format_trading_mode(status['current_mode'])}
⏰ 運行時間: {status.get('uptime', '未知')}

📈 **今日交易統計**
📊 交易筆數: {status.get('total_trades', 0)}
✅ 成功交易: {status.get('successful_trades', 0)}
📈 總 PnL: ${status.get('total_pnl', 0):.2f}
"""
            
            from .keyboards import BotKeyboards
            keyboard = BotKeyboards.get_dashboard_menu(
                user_balance=user_balances,
                system_status=status
            )
            
            await query.edit_message_text(
                dashboard_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            self.logger.error(f"獲取控制台信息失敗: {e}")
            await query.edit_message_text(f"❌ 獲取控制台信息失敗: {str(e)}")
    
    async def handle_trading_hub(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理交易中心主頁面"""
        query = update.callback_query
        await query.answer()
        
        user_id = update.effective_user.id
        
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return
        
        try:
            # 獲取系統狀態
            status = self.bot_controller.get_status()
            current_mode = status['current_mode']
            is_running = status['is_running']
            
            # 生成交易中心信息
            trading_message = f"""
🎯 **交易中心**

🔄 **當前狀態**
{'🟢' if is_running else '🔴'} 模式: {self._format_trading_mode(current_mode)}
{'🔥' if is_running else '💤'} 狀態: {'活躍交易中' if is_running else '等待啟動'}

🎯 **快速模式切換**
點擊以下按鈕切換模式：

🚀 **即時控制**
根據當前狀態選擇操作：
"""
            
            from .keyboards import BotKeyboards
            keyboard = BotKeyboards.get_trading_hub_menu(
                current_mode=self._get_mode_key(current_mode),
                is_running=is_running
            )
            
            await query.edit_message_text(
                trading_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            self.logger.error(f"獲取交易中心信息失敗: {e}")
            await query.edit_message_text(f"❌ 獲取交易中心信息失敗: {str(e)}")
    
    async def handle_analytics_center(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理數據分析中心"""
        query = update.callback_query
        await query.answer()
        
        user_id = update.effective_user.id
        
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return
        
        analytics_message = f"""
📊 **數據分析中心**

📈 **您的交易數據完全私密**
只有您可以查看和管理自己的交易記錄

📋 **可用功能**：
• 交易歷史記錄查看
• 收益和損失分析
• 統計報表和趨勢
• 持倉詳細情況
• 數據導出和備份
• 系統日誌查看

🔒 **隱私保護**：
您的所有數據都經過加密保護，其他用戶無法訪問
"""
        
        from .keyboards import BotKeyboards
        keyboard = BotKeyboards.get_analytics_center_menu()
        
        await query.edit_message_text(
            analytics_message,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
    
    def _get_mode_key(self, trading_mode) -> str:
        """將TradingMode轉換為字串鍵"""
        mode_map = {
            'COPY_TRADING': 'copy',
            'STRATEGY_TRADING': 'strategy',
            'MIXED': 'mixed',
            'DISABLED': 'disabled'
        }
        mode_str = str(trading_mode).replace('TradingMode.', '')
        return mode_map.get(mode_str, 'disabled')
    
    def _format_trading_mode(self, mode: TradingMode) -> str:
        """格式化交易模式顯示"""
        mode_formats = {
            TradingMode.COPY_TRADING: "🎯 V2 跟單模式",
            TradingMode.STRATEGY_TRADING: "📈 V1 策略模式",
            TradingMode.MIXED: "🔀 混合模式",
            TradingMode.DISABLED: "🛑 停用模式"
        }
        return mode_formats.get(mode, "❓ 未知模式")
    
    def _format_trading_mode_short(self, mode: TradingMode) -> str:
        """格式化交易模式短顯示"""
        mode_formats = {
            TradingMode.COPY_TRADING: "V2跟單",
            TradingMode.STRATEGY_TRADING: "V1策略",
            TradingMode.MIXED: "混合",
            TradingMode.DISABLED: "停用"
        }
        return mode_formats.get(mode, "未知")
    
    async def _send_mode_change_notification(self, user_id: int, new_mode: TradingMode) -> None:
        """發送模式切換通知"""
        try:
            from .notifications import NotificationManager
            notification_manager = NotificationManager(self.config)
            
            await notification_manager.send_mode_change_notification(user_id, new_mode)
            
        except Exception as e:
            self.logger.error(f"發送模式切換通知失敗: {e}")

    async def _start_trading_with_updates(self, query, new_mode, user_id: int) -> None:
        """異步啟動交易模式並更新用戶界面"""
        try:
            # 將長時間運行的交易模式作為背景任務啟動
            asyncio.create_task(self.bot_controller.start_trading(new_mode, user_id))

            # 根據不同模式提供不同的等待時間和狀態檢查
            if new_mode == TradingMode.STRATEGY_TRADING:
                # V1 策略模式需要更長的初始化時間
                await asyncio.sleep(3)

                # 發送初始化進度更新
                await query.edit_message_text(
                    f"🔄 {self._format_trading_mode(new_mode)} 正在初始化...\n"
                    "📊 正在掃描市場機會，請稍候..."
                )

                # 再等待一段時間讓市場掃描開始
                await asyncio.sleep(5)

                # 檢查是否成功啟動
                if self.bot_controller.is_running and self.bot_controller.current_mode == new_mode:
                    self.logger.info(f"用戶 {user_id} 的模式 {new_mode.value} 已成功啟動。")

                    from .keyboards import BotKeyboards
                    success_keyboard = BotKeyboards.get_post_action_keyboard("success")

                    await query.edit_message_text(
                        f"✅ **已成功啟動：{self._format_trading_mode(new_mode)}**\n\n"
                        f"🚀 **系統狀態：** 運行中\n"
                        f"📈 **當前活動：** 正在持續掃描市場機會\n"
                        f"🎯 **模式：** {self._format_trading_mode(new_mode)}\n\n"
                        f"**下一步操作：**\n"
                        f"• 點擊「查看狀態」監控實時進度\n"
                        f"• 點擊「檢查錢包」確認餘額\n"
                        f"• 如需停止，點擊「緊急停止」\n\n"
                        f"💡 系統將自動尋找交易機會並執行",
                        reply_markup=success_keyboard,
                        parse_mode=ParseMode.MARKDOWN
                    )
                else:
                    from .keyboards import BotKeyboards
                    error_keyboard = BotKeyboards.get_post_action_keyboard("error")

                    await query.edit_message_text(
                        f"❌ **啟動模式失敗：{self._format_trading_mode(new_mode)}**\n\n"
                        f"**可能原因：**\n"
                        f"• 錢包餘額不足（需要 USDC > $10, MATIC > 0.1）\n"
                        f"• 網路連接問題\n"
                        f"• 系統組件初始化失敗\n\n"
                        f"**建議操作：**\n"
                        f"• 點擊「檢查錢包」確認餘額\n"
                        f"• 點擊「重新嘗試」再次啟動\n"
                        f"• 點擊「聯繫支援」獲得幫助",
                        reply_markup=error_keyboard,
                        parse_mode=ParseMode.MARKDOWN
                    )
            else:
                # 其他模式使用較短的等待時間
                await asyncio.sleep(2)

                if self.bot_controller.is_running and self.bot_controller.current_mode == new_mode:
                    self.logger.info(f"用戶 {user_id} 的模式 {new_mode.value} 已成功啟動。")

                    from .keyboards import BotKeyboards
                    success_keyboard = BotKeyboards.get_post_action_keyboard("success")

                    await query.edit_message_text(
                        f"✅ **已成功啟動：{self._format_trading_mode(new_mode)}**\n\n"
                        f"🚀 **系統狀態：** 運行中\n"
                        f"🎯 **當前模式：** {self._format_trading_mode(new_mode)}\n\n"
                        f"**下一步操作：**\n"
                        f"• 點擊「查看狀態」監控系統運行\n"
                        f"• 點擊「檢查錢包」確認餘額\n"
                        f"• 如需停止，點擊「緊急停止」",
                        reply_markup=success_keyboard,
                        parse_mode=ParseMode.MARKDOWN
                    )
                else:
                    from .keyboards import BotKeyboards
                    error_keyboard = BotKeyboards.get_post_action_keyboard("error")

                    await query.edit_message_text(
                        f"❌ **啟動模式失敗：{self._format_trading_mode(new_mode)}**\n\n"
                        f"**可能原因：**\n"
                        f"• 錢包餘額不足\n"
                        f"• 網路連接問題\n"
                        f"• 系統組件未就緒\n\n"
                        f"**建議操作：**\n"
                        f"• 點擊「檢查錢包」確認餘額\n"
                        f"• 點擊「重新嘗試」再次啟動\n"
                        f"• 點擊「聯繫支援」獲得幫助",
                        reply_markup=error_keyboard,
                        parse_mode=ParseMode.MARKDOWN
                    )

            # 發送通知
            await self._send_mode_change_notification(user_id, new_mode)

        except Exception as e:
            self.logger.error(f"異步啟動交易模式失敗: {e}", exc_info=True)
            try:
                await query.edit_message_text(f"❌ 啟動模式失敗: {str(e)}")
            except Exception as edit_error:
                self.logger.error(f"編輯消息失敗: {edit_error}")

    async def _show_auto_authorization_flow(self, update: Update, user_id: int, username: str) -> None:
        """顯示自動授權流程"""
        auto_auth_message = f"""
🚀 **歡迎使用 Polymarket CopyBot V2.0**

👋 Hello {username}!

您可以直接開始使用機器人，但請先閱讀重要的安全提醒：

🛡️ **重要風險提醒：**
• 💰 此為加密貨幣自動交易系統，存在資金損失風險
• 🔒 您將擁有完全獨立的加密錢包，由您自行管理
• 🎯 建議先使用小額資金進行測試
• ⚠️ 交易有風險，請僅投入您能負擔損失的資金

🔐 **安全特性：**
• 每個用戶擁有独立的以太坊錢包（不共享）
• 私鑰加密存儲，無法被其他用戶訪問
• 資金完全隔離，不會與其他用戶混淆

🎉 **立即開始使用：**
点击下方「確認風險並繼續」按鈕，系統將：
1. 自動為您創建獨立的加密錢包
2. 啟用所有交易功能
3. 提供完整的使用指導

❓ 如有疑問，可先點擊「了解更多」查看詳細說明
"""
        
        # 創建特殊的確認按鈕
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("📝 了解更多詳細信息", callback_data="auto_auth_info")],
            [InlineKeyboardButton("✅ 確認風險並繼續", callback_data="auto_auth_confirm")],
            [InlineKeyboardButton("❌ 取消", callback_data="auto_auth_cancel")]
        ])
        
        await update.message.reply_text(
            auto_auth_message,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def _show_manual_authorization_request(self, update: Update, user_id: int, username: str) -> None:
        """顯示手動授權申請"""
        manual_auth_message = f"""
🙅‍♂️ **需要管理員授權**

您好 {username}！

系統管理員已關閉自動授權功能，您需要管理員手動授權才能使用。

🔐 **申請授權：**
請聯繫管理員並提供以下信息：
• 用戶 ID：`{user_id}`
• 用戶名：@{username}

🛡️ **安全提醒：**
此機器人用於自動化加密貨幣交易，僅授權信任的用戶使用。
"""
        
        await update.message.reply_text(
            manual_auth_message,
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def handle_auto_authorization(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理自動授權回調"""
        query = update.callback_query
        await query.answer()
        
        user_id = update.effective_user.id
        username = update.effective_user.username or "未知用戶"
        
        if query.data == "auto_auth_confirm":
            # 用戶確認風險，完成自動授權
            success = await self.session_manager.complete_auto_authorization(user_id, risk_acknowledged=True)
            
            if success:
                # 授權成功，創建錢包並顯示歡迎信息
                wallet_created = self.session_manager.ensure_user_wallet(user_id, username)
                
                if wallet_created:
                    wallet_address = self.session_manager.get_user_wallet_address(user_id)
                    
                    success_message = f"""
🎉 **授權成功！歡迎使用 Polymarket CopyBot**

🎉 {username}，您現在可以開始使用所有功能！

💰 **您的獨立錢包已創建：**
`{wallet_address}`

**可用功能：**
🎯 `/status` - 查看系統狀態
📊 `/balance` - 查看您的錢包餘額
🔄 `/mode` - 切換交易模式
❓ `/help` - 查看幫助

**下一步：**
1. 點擊 `/balance` 查看錢包詳情
2. 小額充值進行測試
3. 選擇適合的交易模式

❤️ 感謝您的信任！
"""
                else:
                    success_message = "✅ 授權成功！但錢包創建失敗，請聯繫管理員。"
                
                await query.edit_message_text(
                    success_message,
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text("❌ 授權失敗，請稍後再試或聯繫管理員。")
        
        elif query.data == "auto_auth_info":
            # 顯示詳細信息
            info_message = """
📝 **詳細安全信息**

**錢包安全：**
• 🔒 每個用戶擁有独立的以太坊錢包地址
• 🔐 私鑰使用 PBKDF2 + 用戶ID鹽值加密
• 🛡️ 錢包檔案權限設為600（僅所有者可讀寫）
• 💰 資金與其他用戶完全隔離

**系統功能：**
• 🎯 V2 跟單模式：跟隨聪明錢錢包的交易
• 📈 V1 策略模式：基於預設條件的策略交易
• 🔄 混合模式：同時運行兩種模式

**風險提醒：**
• ⚠️ 加密貨幣交易有高風險，可能導致資金完全損失
• 💰 請僅投入您能負擔完全損失的資金
• 🎯 建議先用 $10-50 進行測試
• 🔍 在參與交易前，請充分理解風險

點擊下方按鈕繼續選擇：
"""
            
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("✅ 我理解風險，繼續使用", callback_data="auto_auth_confirm")],
                [InlineKeyboardButton("❌ 取消使用", callback_data="auto_auth_cancel")]
            ])
            
            await query.edit_message_text(
                info_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        
        elif query.data == "auto_auth_cancel":
            await query.edit_message_text(
                "❌ 已取消授權。如需使用，請再次點擊 /start 命令。"
            )
    
    def _check_user_authorization(self, user_id: int) -> bool:
        """檢查用戶授權狀態

        Args:
            user_id: 用戶 ID

        Returns:
            是否已授權
        """
        return self.session_manager.is_authorized(user_id)

    def _get_mode_description(self, mode: TradingMode) -> str:
        """獲取交易模式的詳細說明

        Args:
            mode: 交易模式

        Returns:
            模式說明文字
        """
        descriptions = {
            TradingMode.COPY_TRADING: "🎯 **V2 跟單模式**: 監控聰明錢錢包，自動跟隨高勝率交易者的策略",
            TradingMode.STRATEGY_TRADING: "📈 **V1 策略模式**: 掃描即將到期的高概率市場，基於預設條件自動交易",
            TradingMode.MIXED: "🔀 **混合模式**: 同時運行跟單和策略模式，最大化交易機會",
            TradingMode.DISABLED: "🛑 **停用模式**: 停止所有自動交易功能"
        }
        return descriptions.get(mode, "未知模式")

    async def handle_status_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理狀態查詢回調"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id

        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        # 重用現有的狀態命令邏輯，但使用回調查詢
        try:
            status = self.bot_controller.get_status()
            user_balances = self.session_manager.get_user_balance(user_id)

            status_message = f"""
📊 **系統狀態報告**

**🤖 系統狀態：**
{'🟢' if status['is_running'] else '🔴'} 狀態：{'運行中' if status['is_running'] else '已停止'}
🎯 當前模式：{self._format_trading_mode(status['current_mode'])}
⏰ 運行時間：{status.get('uptime', '未知')}

**💰 您的錢包：**
💵 USDC：${user_balances.get('USDC', 0):.2f}
⛽ MATIC：{user_balances.get('MATIC', 0):.4f}

**📈 交易統計：**
🎯 發現機會：{status.get('opportunities_found', 0)}
💼 執行交易：{status.get('trades_executed', 0)}
📊 成功率：{status.get('success_rate', 0):.1f}%
"""

            from .keyboards import BotKeyboards
            keyboard = BotKeyboards.get_post_action_keyboard("general")

            await query.edit_message_text(
                status_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"獲取狀態失敗: {e}")
            await query.edit_message_text(f"❌ 獲取狀態失敗: {str(e)}")

    async def handle_balance_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理餘額查詢回調"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id

        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        try:
            user_balances = self.session_manager.get_user_balance(user_id)
            wallet_address = self.session_manager.get_user_wallet_address(user_id)

            usdc_balance = user_balances.get('USDC', 0)
            matic_balance = user_balances.get('MATIC', 0)

            # 評估餘額狀態
            usdc_status = "✅ 充足" if usdc_balance >= 20 else "⚠️ 建議增加" if usdc_balance >= 10 else "❌ 不足"
            matic_status = "✅ 充足" if matic_balance >= 0.5 else "⚠️ 建議增加" if matic_balance >= 0.1 else "❌ 不足"

            balance_message = f"""
💰 **錢包餘額詳情**

**🏦 您的專屬錢包：**
`{wallet_address}`

**💵 當前餘額：**
• USDC：${usdc_balance:.2f} {usdc_status}
• MATIC：{matic_balance:.4f} {matic_status}

**💡 餘額建議：**
• 交易最低要求: USDC > 50, POL > 10
• 建議要求: USDC > 100, POL > 20

**🔒 安全提醒：**
• 這是您的專屬錢包，與其他用戶完全隔離
• 私鑰經過加密保護，安全可靠
• 建議定期備份錢包信息
"""

            from .keyboards import BotKeyboards
            keyboard = BotKeyboards.get_post_action_keyboard("general")

            await query.edit_message_text(
                balance_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"獲取餘額失敗: {e}")
            await query.edit_message_text(f"❌ 獲取餘額失敗: {str(e)}")

    # ==================== 第一批：基础导航和菜单功能 ====================
    
    async def handle_back_navigation(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """处理返回按钮"""
        query = update.callback_query
        await query.answer()
        
        user_id = update.effective_user.id
        
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授权用户")
            return
        
        # 返回主菜单
        await self._show_main_menu(query, user_id)
    
    async def handle_main_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """处理主菜单按钮"""
        query = update.callback_query
        await query.answer()
        
        user_id = update.effective_user.id
        
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授权用户")
            return
        
        await self._show_main_menu(query, user_id)
    
    async def handle_settings_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """处理设置菜单"""
        query = update.callback_query
        await query.answer()
        
        user_id = update.effective_user.id
        
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授权用户")
            return
        
        settings_message = """
⚙️ **系统设置**

🔧 **可用设置项目：**
• 🔔 通知设置
• 🛡️ 安全设置
• 🎯 风险管理
• 📊 性能优化
• 🌐 API配置
• 📝 日志设置
• 🗄️ 数据备份
• 👥 用户管理
• 🔄 系统维护

选择要配置的设置项目：
"""
        
        from .keyboards import BotKeyboards
        keyboard = BotKeyboards.get_enhanced_settings_menu("basic")
        
        await query.edit_message_text(
            settings_message,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def handle_history_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """处理历史记录菜单"""
        query = update.callback_query
        await query.answer()
        
        user_id = update.effective_user.id
        
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授权用户")
            return
        
        try:
            # 获取用户交易历史（这里暂时显示模拟数据）
            history_message = f"""
📊 **您的交易历史记录**

🔒 **隐私保护：** 您的交易记录完全私密，其他用户无法查看

📈 **近期交易概览：**
• 总交易数：0
• 成功交易：0
• 失败交易：0
• 总 PnL：$0.00

📋 **详细记录：**
暂无交易记录

💡 **提示：** 开始交易后，所有记录将在此显示
"""
            
            from .keyboards import BotKeyboards
            keyboard = BotKeyboards.get_navigation_keyboard(1, 1, "history")
            
            await query.edit_message_text(
                history_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            self.logger.error(f"获取交易历史失败: {e}")
            await query.edit_message_text(f"❌ 获取交易历史失败: {str(e)}")
    
    async def handle_trading_data_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """处理交易数据菜单"""
        query = update.callback_query
        await query.answer()
        
        user_id = update.effective_user.id
        
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授权用户")
            return
        
        try:
            # 获取系统状态
            status = self.bot_controller.get_status()
            
            trading_data_message = f"""
📊 **实时交易数据**

🤖 **系统状态：**
{'🟢' if status['is_running'] else '🔴'} 运行状态：{'运行中' if status['is_running'] else '已停止'}
🎯 当前模式：{self._format_trading_mode(status['current_mode'])}

📈 **今日统计：**
📊 总交易数：{status.get('total_trades', 0)}
✅ 成功交易：{status.get('successful_trades', 0)}
❌ 失败交易：{status.get('failed_trades', 0)}
💰 总 PnL：${status.get('total_pnl', 0):.2f}
📊 胜率：{status.get('win_rate', 0):.1f}%

⚡ **性能指标：**
🚀 平均延迟：{status.get('avg_latency', 0):.2f}s
💼 当前持仓：{status.get('current_positions', 0)}
⏰ 运行时间：{status.get('uptime', '未知')}
"""
            
            from .keyboards import BotKeyboards
            keyboard = BotKeyboards.get_analytics_center_menu()
            
            await query.edit_message_text(
                trading_data_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            self.logger.error(f"获取交易数据失败: {e}")
            await query.edit_message_text(f"❌ 获取交易数据失败: {str(e)}")
    
    async def handle_page_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """处理页面信息显示"""
        query = update.callback_query
        await query.answer("📄 页面信息", show_alert=True)

    # ==================== 第二批：核心交易功能 - 紧急控制 ====================

    async def handle_emergency_stop(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """处理紧急停止"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id

        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授权用户")
            return

        try:
            # 执行紧急停止
            await self.bot_controller.emergency_stop()

            await query.edit_message_text(
                "🛑 **紧急停止已执行**\n\n"
                "✅ 所有交易活动已停止\n"
                "✅ 当前订单已取消\n"
                "✅ 系统进入安全模式\n\n"
                "如需重新启动，请使用相应的启动命令。",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"紧急停止失败: {e}")
            await query.edit_message_text(f"❌ 紧急停止失败: {str(e)}")

    async def handle_emergency_stop_all(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """处理全系统紧急停止"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id

        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授权用户")
            return

        try:
            # 执行全系统紧急停止
            await self.bot_controller.emergency_stop_all()

            await query.edit_message_text(
                "🚨 **全系统紧急停止已执行**\n\n"
                "🛑 所有交易系统已停止\n"
                "🛑 V1和V2系统已关闭\n"
                "🛑 所有监控已暂停\n"
                "🛑 系统进入完全安全模式\n\n"
                "⚠️ 需要管理员手动重启系统",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"全系统紧急停止失败: {e}")
            await query.edit_message_text(f"❌ 全系统紧急停止失败: {str(e)}")

    async def handle_emergency_risk_protection(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """处理紧急风险保护"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id

        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授权用户")
            return

        try:
            # 激活风险保护模式
            protection_status = await self.bot_controller.activate_risk_protection()

            await query.edit_message_text(
                "🛡️ **紧急风险保护已激活**\n\n"
                "✅ 交易金额限制已降低\n"
                "✅ 风险监控已加强\n"
                "✅ 自动止损已启用\n"
                "✅ 异常检测已激活\n\n"
                f"🔒 当前保护级别：{protection_status.get('level', '高')}",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"激活风险保护失败: {e}")
            await query.edit_message_text(f"❌ 激活风险保护失败: {str(e)}")

    async def handle_emergency_lock_wallet(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """处理紧急锁定钱包"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id

        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授权用户")
            return

        try:
            # 锁定用户钱包
            lock_status = self.session_manager.lock_user_wallet(user_id)

            await query.edit_message_text(
                "🔒 **钱包已紧急锁定**\n\n"
                "🛑 所有交易功能已禁用\n"
                "🛑 资金转移已阻止\n"
                "🛑 API访问已限制\n\n"
                "⚠️ 如需解锁，请联系管理员\n"
                "📞 或使用紧急联系功能",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"锁定钱包失败: {e}")
            await query.edit_message_text(f"❌ 锁定钱包失败: {str(e)}")

    async def handle_emergency_contact_admin(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """处理紧急联系管理员"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        username = query.from_user.username or "未知用户"

        try:
            # 发送紧急通知给管理员
            admin_message = f"""
🚨 **紧急联系请求**

👤 用户：{username} (ID: {user_id})
⏰ 时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🆘 状态：请求紧急协助

请尽快处理此紧急请求。
"""

            # 这里应该发送给管理员，暂时记录日志
            self.logger.warning(f"紧急联系请求 - 用户: {user_id}, 用户名: {username}")

            await query.edit_message_text(
                "📞 **紧急联系已发送**\n\n"
                "✅ 您的紧急请求已发送给管理员\n"
                "✅ 管理员将尽快回复\n"
                "✅ 请保持Telegram在线\n\n"
                "⏰ 预计回复时间：15分钟内\n"
                "🆘 如情况紧急，请直接联系客服",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"联系管理员失败: {e}")
            await query.edit_message_text(f"❌ 联系管理员失败: {str(e)}")

    async def handle_emergency_export_logs(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """处理紧急导出日志"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id

        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授权用户")
            return

        try:
            # 导出用户相关日志
            log_summary = await self._generate_emergency_log_summary(user_id)

            await query.edit_message_text(
                f"📋 **紧急日志导出**\n\n"
                f"📊 **日志摘要：**\n"
                f"⏰ 时间范围：最近24小时\n"
                f"📈 交易记录：{log_summary.get('trades', 0)}笔\n"
                f"⚠️ 错误记录：{log_summary.get('errors', 0)}个\n"
                f"🔔 警告记录：{log_summary.get('warnings', 0)}个\n\n"
                f"✅ 日志已生成并保存\n"
                f"📁 文件位置：logs/emergency_export_{user_id}.log\n\n"
                f"如需详细日志，请联系管理员",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"导出日志失败: {e}")
            await query.edit_message_text(f"❌ 导出日志失败: {str(e)}")

    async def _generate_emergency_log_summary(self, user_id: int) -> dict:
        """生成紧急日志摘要"""
        try:
            # 这里应该实际查询日志数据库或文件
            # 暂时返回模拟数据
            return {
                'trades': 5,
                'errors': 1,
                'warnings': 3,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"生成日志摘要失败: {e}")
            return {'trades': 0, 'errors': 0, 'warnings': 0}
    
    async def _show_main_menu(self, query, user_id: int) -> None:
        """显示主菜单"""
        try:
            username = query.from_user.username or "未知用户"
            
            # 确保用户有钱包
            wallet_created = self.session_manager.ensure_user_wallet(user_id, username)
            
            if not wallet_created:
                await query.edit_message_text("❌ 无法获取您的专属钱包，请联系管理员")
                return
            
            # 获取系统状态信息
            status = self.bot_controller.get_status()
            current_mode = self._format_trading_mode_short(status['current_mode'])
            is_running = status['is_running']
            
            # 获取用户钱包状态
            user_balances = self.session_manager.get_user_balance(user_id)
            has_balance = user_balances.get('USDC', 0) > 10
            
            main_menu_message = f"""
🏠 **主菜单**

👋 欢迎回来！

🤖 **系统状态：**
{'🟢' if is_running else '🔴'} 状态：{'运行中' if is_running else '已停止'}
🎯 模式：{current_mode}

💰 **您的钱包：**
💵 USDC：${user_balances.get('USDC', 0):.2f}
⛽ MATIC：{user_balances.get('MATIC', 0):.4f}

选择要执行的操作：
"""
            
            from .keyboards import BotKeyboards
            keyboard = BotKeyboards.get_main_menu(
                is_running=is_running,
                current_mode=current_mode,
                has_balance=has_balance
            )
            
            await query.edit_message_text(
                main_menu_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            self.logger.error(f"显示主菜单失败: {e}")
            await query.edit_message_text(f"❌ 显示主菜单失败: {str(e)}")
    
    async def _handle_unimplemented_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理所有未實現的回調"""
        query = update.callback_query
        await query.answer()
        
        user_id = query.from_user.id
        callback_data = query.data
        
        self.logger.warning(f"用戶 {user_id} 觸發了未實現的回調: {callback_data}")
        
        message = f"""
        🚧 **功能開發中** 🚧
        
        您點擊的功能 `({callback_data})` 正在開發中，很快就會上線！
        
        感謝您的耐心等待。
        """
        
        from .keyboards import BotKeyboards
        keyboard = BotKeyboards.get_post_action_keyboard("general")
        
        await query.edit_message_text(
            message,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )

    # ==================== 新UI處理器 - 按照UI_spec.md實現 ====================

    async def handle_my_wallet(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理我的錢包按鈕 - 錢包狀態總覽頁面"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        try:
            # 獲取用戶錢包信息
            username = update.effective_user.username or "未知用戶"
            wallet_created = self.session_manager.ensure_user_wallet(user_id, username)

            if not wallet_created:
                await query.edit_message_text("❌ 無法獲取您的專屬錢包，請聯繫管理員")
                return

            wallet_address = self.session_manager.get_user_wallet_address(user_id)
            user_balances = self.session_manager.get_user_balance(user_id)

            usdc_balance = user_balances.get('USDC', 0)
            matic_balance = user_balances.get('MATIC', 0)

            # 資金建議狀態
            if usdc_balance >= 50 and matic_balance >= 0.5:
                balance_status = "🟢 **順暢交易** - 餘額充足"
            elif usdc_balance >= 10 and matic_balance >= 0.1:
                balance_status = "🟡 **最低啟動** - 建議增加餘額"
            else:
                balance_status = "🔴 **需要充值** - 餘額不足"

            wallet_message = f"""💰 **錢包狀態總覽**

🏦 **您的專屬交易錢包地址：**
`{wallet_address}`
*(點擊可複製地址)*

---
**💵 當前餘額**

• **USDC (交易資金):** `${usdc_balance:.2f}`
• **MATIC (網路燃料):** `{matic_balance:.4f}`

🔍 **為何需要這兩種代幣？**
* **USDC** 是您在 Polymarket 上進行預測交易的**本金**。
* **MATIC** 是支付 Polygon 網路交易手續費 (Gas Fee) 的**燃料**，不可或缺。
---
💡 **資金建議**
• **最低啟動:** USDC ≥ $10, MATIC ≥ 0.1
• **順暢交易:** USDC ≥ $50, MATIC ≥ 0.5

{balance_status}"""

            from .keyboards import BotKeyboards
            keyboard = BotKeyboards.get_wallet_status_menu()

            await query.edit_message_text(
                wallet_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"顯示錢包狀態失敗: {e}")
            await query.edit_message_text(f"❌ 顯示錢包狀態失敗: {str(e)}")

    async def handle_strategy_bot(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理策略機器人按鈕 - 策略機器人管理頁面"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        try:
            # 獲取用戶的策略設定
            user_settings = self.session_manager.get_user_setting(user_id, "strategy_settings", {})
            win_rate = user_settings.get("min_win_rate", 85.0)
            expiry_days = user_settings.get("max_expiry_days", 1)
            trade_amount = user_settings.get("trade_amount", 10.0)

            # 獲取系統狀態
            status = self.bot_controller.get_status()
            is_running = status['is_running'] and status['current_mode'] in ['V1策略', '混合']

            # 獲取今日績效（模擬數據，實際應從數據庫獲取）
            today_pnl = status.get('daily_pnl', 0.0)

            strategy_message = f"""📈 **策略機器人管理**

**當前狀態：** {'🟢 運行中' if is_running else '🔴 已停止'}

**策略摘要：**
• **觸發條件：** 勝率 > `{win_rate}%` 且 到期日 < `{expiry_days}` 天
• **單筆金額：** `${trade_amount}` USDC

**今日績效：** `${today_pnl:.2f}` USDC"""

            from .keyboards import BotKeyboards
            keyboard = BotKeyboards.get_strategy_bot_menu(
                is_active=is_running,
                win_rate=win_rate,
                expiry_days=expiry_days
            )

            await query.edit_message_text(
                strategy_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"顯示策略機器人管理失敗: {e}")
            await query.edit_message_text(f"❌ 顯示策略機器人管理失敗: {str(e)}")

    async def handle_strategy_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理策略設定按鈕 - 策略機器人設定頁面"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        try:
            # 獲取用戶當前的策略設定
            user_settings = self.session_manager.get_user_setting(user_id, "strategy_settings", {})
            win_rate = user_settings.get("min_win_rate", 85.0)
            expiry_days = user_settings.get("max_expiry_days", 1)
            trade_amount = user_settings.get("trade_amount", 10.0)

            settings_message = f"""🛠️ **策略機器人設定**

請設定您的自動化交易策略參數。

**--- 當前配置 ---**
• **📈 最低勝率:** `{win_rate}%` (預設: 85%)
• **🗓️ 最長到期日:** `{expiry_days}` 天 (預設: 1)
• **💰 固定下單金額:** `${trade_amount}` USDC (預設: 10)
**------------------**

👇 請點擊下方按鈕進行修改。"""

            from .keyboards import BotKeyboards
            keyboard = BotKeyboards.get_strategy_settings_menu()

            await query.edit_message_text(
                settings_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"顯示策略設定失敗: {e}")
            await query.edit_message_text(f"❌ 顯示策略設定失敗: {str(e)}")

    async def handle_copy_bot(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理跟單機器人按鈕 - 跟單機器人管理頁面"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        try:
            # 獲取用戶的跟單設定
            copy_traders = self.session_manager.get_user_setting(user_id, "copy_traders", [])
            trader_count = len(copy_traders)

            if trader_count > 0:
                trader_list = "\n".join([
                    f"{i+1}. `{trader.get('nickname', '未命名')}` -> `{trader.get('wallet_address', '')[:10]}...{trader.get('wallet_address', '')[-6:]}`"
                    for i, trader in enumerate(copy_traders[:5])  # 只顯示前5個
                ])
                if trader_count > 5:
                    trader_list += f"\n... 還有 {trader_count - 5} 個跟單設定"
            else:
                trader_list = "您尚未設定任何跟單"

            copy_message = f"""👥 **跟單機器人管理**

您目前正在跟單 `{trader_count}` 位交易員。
選擇下方選項來新增或管理您的跟單設定。

**當前跟單列表：**
{trader_list}"""

            from .keyboards import BotKeyboards
            keyboard = BotKeyboards.get_copy_bot_menu(monitored_count=trader_count)

            await query.edit_message_text(
                copy_message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"顯示跟單機器人管理失敗: {e}")
            await query.edit_message_text(f"❌ 顯示跟單機器人管理失敗: {str(e)}")

    async def handle_add_copy_trader(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理新增跟單交易者按鈕"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        # 設定用戶操作狀態
        self.session_manager.set_user_operation(user_id, "adding_copy_trader", {})

        await query.edit_message_text(
            "👤 **新增跟單交易者**\n\n請輸入要跟單的錢包地址：\n\n"
            "📝 格式：0x開頭的42位元地址\n"
            "💡 例如：0x1234567890abcdef1234567890abcdef12345678",
            parse_mode=ParseMode.MARKDOWN
        )

    async def handle_copy_trader_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理跟單設定按鈕"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        try:
            # 獲取用戶跟單設定
            copy_settings = self.session_manager.get_user_setting(user_id, "copy_settings", {
                "default_copy_mode": "percentage",
                "default_copy_percentage": 10.0,
                "default_copy_amount": 50.0,
                "max_copy_amount": 500.0,
                "auto_copy_enabled": True
            })

            keyboard = self.keyboards.get_copy_trader_settings_menu()

            await query.edit_message_text(
                f"⚙️ **跟單設定**\n\n"
                f"📊 **當前設定**：\n"
                f"• 跟單模式：{'百分比跟單' if copy_settings.get('default_copy_mode') == 'percentage' else '固定金額跟單'}\n"
                f"• 跟單比例：{copy_settings.get('default_copy_percentage', 10)}%\n"
                f"• 固定金額：${copy_settings.get('default_copy_amount', 50)} USDC\n"
                f"• 最大金額：${copy_settings.get('max_copy_amount', 500)} USDC\n"
                f"• 自動跟單：{'✅ 啟用' if copy_settings.get('auto_copy_enabled') else '❌ 停用'}\n\n"
                f"請選擇要修改的設定：",
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"顯示跟單設定失敗: {e}")
            await query.edit_message_text(f"❌ 顯示跟單設定失敗: {str(e)}")

    async def handle_set_copy_mode(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理設定跟單模式按鈕"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        # 切換跟單模式
        current_mode = self.session_manager.get_user_setting(user_id, "copy_settings", {}).get("default_copy_mode", "percentage")
        new_mode = "fixed_amount" if current_mode == "percentage" else "percentage"

        self.session_manager.update_user_setting(user_id, "copy_settings.default_copy_mode", new_mode)

        mode_text = "固定金額跟單" if new_mode == "fixed_amount" else "百分比跟單"

        await query.edit_message_text(
            f"🔄 **跟單模式已切換**\n\n✅ 當前模式：{mode_text}\n\n正在返回設定頁面...",
            parse_mode=ParseMode.MARKDOWN
        )

        # 2秒後返回設定頁面
        await asyncio.sleep(2)
        await self.handle_copy_trader_settings(update, context)

    async def handle_set_copy_percentage(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理設定跟單比例按鈕"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        # 設定用戶操作狀態
        self.session_manager.set_user_operation(user_id, "setting_copy_percentage", {})

        await query.edit_message_text(
            "📊 **設定跟單比例**\n\n請輸入跟單比例 (1-100 之間的數字，例如：10 代表跟單10%)：",
            parse_mode=ParseMode.MARKDOWN
        )

    async def handle_set_copy_amount(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理設定固定跟單金額按鈕"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        # 設定用戶操作狀態
        self.session_manager.set_user_operation(user_id, "setting_copy_amount", {})

        await query.edit_message_text(
            "💰 **設定固定跟單金額**\n\n請輸入每筆跟單的固定 USDC 金額 (例如：50)：",
            parse_mode=ParseMode.MARKDOWN
        )

    async def handle_set_win_rate(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理設定最低勝率按鈕"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        # 設定用戶操作狀態
        self.session_manager.set_user_operation(user_id, "setting_win_rate", {})

        await query.edit_message_text(
            "📈 **設定最低勝率**\n\n請輸入觸發交易的最低市場勝率 (0-100 之間的數字，例如：85)",
            parse_mode=ParseMode.MARKDOWN
        )

    async def handle_set_expiry_days(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理設定到期日按鈕"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        # 設定用戶操作狀態
        self.session_manager.set_user_operation(user_id, "setting_expiry_days", {})

        await query.edit_message_text(
            "🗓️ **設定到期日**\n\n請輸入最長的市場到期天數 (例如：輸入 1 代表只交易 24 小時內到期的市場)",
            parse_mode=ParseMode.MARKDOWN
        )

    async def handle_set_trade_amount(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理設定下單金額按鈕"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        # 設定用戶操作狀態
        self.session_manager.set_user_operation(user_id, "setting_trade_amount", {})

        await query.edit_message_text(
            "💰 **設定下單金額**\n\n請輸入您希望每筆策略交易的固定 USDC 金額 (例如：10)",
            parse_mode=ParseMode.MARKDOWN
        )

    async def handle_reset_strategy_defaults(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理恢復預設值按鈕"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        try:
            # 重置用戶策略設定
            self.session_manager.reset_user_strategy_settings(user_id)

            await query.edit_message_text(
                "🔄 **恢復預設值**\n\n✅ 已將所有策略參數恢復為系統預設值：\n• 最低勝率：85%\n• 最長到期日：1天\n• 固定下單金額：$10 USDC",
                parse_mode=ParseMode.MARKDOWN
            )

            # 2秒後自動返回設定頁面
            await asyncio.sleep(2)
            await self.handle_strategy_settings(update, context)

        except Exception as e:
            self.logger.error(f"重置策略設定失敗: {e}")
            await query.edit_message_text(f"❌ 重置策略設定失敗: {str(e)}")

    async def handle_save_strategy_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理保存策略設定按鈕"""
        query = update.callback_query
        await query.answer()

        user_id = update.effective_user.id
        if not self._check_user_authorization(user_id):
            await query.edit_message_text("❌ 未授權用戶")
            return

        try:
            # 獲取當前設定
            user_settings = self.session_manager.get_user_setting(user_id, "strategy_settings", {})

            await query.edit_message_text(
                "✅ **策略設定已保存**\n\n您的策略參數已成功保存並將在下次交易時生效。",
                parse_mode=ParseMode.MARKDOWN
            )

            # 2秒後返回策略機器人管理頁面
            await asyncio.sleep(2)
            await self.handle_strategy_bot(update, context)

        except Exception as e:
            self.logger.error(f"保存策略設定失敗: {e}")
            await query.edit_message_text(f"❌ 保存策略設定失敗: {str(e)}")

    async def handle_text_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """處理用戶文字輸入 - 策略參數設定"""
        user_id = update.effective_user.id
        text = update.message.text.strip()

        if not self._check_user_authorization(user_id):
            await update.message.reply_text("❌ 未授權用戶")
            return

        # 獲取用戶當前操作狀態
        operation = self.session_manager.get_user_operation(user_id)
        if not operation:
            # 如果沒有操作狀態，忽略輸入
            return

        operation_type = operation.get("operation")

        try:
            if operation_type == "setting_win_rate":
                await self._handle_win_rate_input(update, text, user_id)
            elif operation_type == "setting_expiry_days":
                await self._handle_expiry_days_input(update, text, user_id)
            elif operation_type == "setting_trade_amount":
                await self._handle_trade_amount_input(update, text, user_id)
            elif operation_type == "adding_copy_trader":
                await self._handle_copy_trader_address_input(update, text, user_id)
            elif operation_type == "setting_copy_percentage":
                await self._handle_copy_percentage_input(update, text, user_id)
            elif operation_type == "setting_copy_amount":
                await self._handle_copy_amount_input(update, text, user_id)
            else:
                # 清除操作狀態
                self.session_manager.clear_user_operation(user_id)

        except Exception as e:
            self.logger.error(f"處理用戶輸入失敗: {e}")
            await update.message.reply_text(f"❌ 處理輸入失敗: {str(e)}")
            self.session_manager.clear_user_operation(user_id)

    async def _handle_win_rate_input(self, update: Update, text: str, user_id: int) -> None:
        """處理勝率輸入"""
        try:
            win_rate = float(text)
            if not (0 <= win_rate <= 100):
                await update.message.reply_text("❌ 勝率必須在 0-100 之間，請重新輸入：")
                return

            # 更新用戶設定
            self.session_manager.update_user_strategy_setting(user_id, "min_win_rate", win_rate)

            await update.message.reply_text(
                f"✅ **勝率設定成功**\n\n最低勝率已設定為：`{win_rate}%`\n\n正在返回設定頁面...",
                parse_mode=ParseMode.MARKDOWN
            )

            # 清除操作狀態
            self.session_manager.clear_user_operation(user_id)

            # 2秒後返回設定頁面
            await asyncio.sleep(2)
            # 創建一個假的callback query來重用現有的處理器
            from telegram import CallbackQuery
            fake_query = CallbackQuery(
                id="fake",
                from_user=update.effective_user,
                chat_instance="fake",
                data="strategy_settings"
            )
            fake_update = Update(update_id=0, callback_query=fake_query)
            await self.handle_strategy_settings(fake_update, None)

        except ValueError:
            await update.message.reply_text("❌ 請輸入有效的數字 (0-100)：")

    async def _handle_expiry_days_input(self, update: Update, text: str, user_id: int) -> None:
        """處理到期天數輸入"""
        try:
            expiry_days = int(text)
            if not (1 <= expiry_days <= 30):
                await update.message.reply_text("❌ 到期天數必須在 1-30 之間，請重新輸入：")
                return

            # 更新用戶設定
            self.session_manager.update_user_strategy_setting(user_id, "max_expiry_days", expiry_days)

            await update.message.reply_text(
                f"✅ **到期日設定成功**\n\n最長到期日已設定為：`{expiry_days}` 天\n\n正在返回設定頁面...",
                parse_mode=ParseMode.MARKDOWN
            )

            # 清除操作狀態
            self.session_manager.clear_user_operation(user_id)

            # 2秒後返回設定頁面
            await asyncio.sleep(2)
            from telegram import CallbackQuery
            fake_query = CallbackQuery(
                id="fake",
                from_user=update.effective_user,
                chat_instance="fake",
                data="strategy_settings"
            )
            fake_update = Update(update_id=0, callback_query=fake_query)
            await self.handle_strategy_settings(fake_update, None)

        except ValueError:
            await update.message.reply_text("❌ 請輸入有效的整數 (1-30)：")

    async def _handle_trade_amount_input(self, update: Update, text: str, user_id: int) -> None:
        """處理交易金額輸入"""
        try:
            trade_amount = float(text)
            if not (1 <= trade_amount <= 1000):
                await update.message.reply_text("❌ 交易金額必須在 1-1000 USDC 之間，請重新輸入：")
                return

            # 更新用戶設定
            self.session_manager.update_user_strategy_setting(user_id, "trade_amount", trade_amount)

            await update.message.reply_text(
                f"✅ **交易金額設定成功**\n\n固定下單金額已設定為：`${trade_amount}` USDC\n\n正在返回設定頁面...",
                parse_mode=ParseMode.MARKDOWN
            )

            # 清除操作狀態
            self.session_manager.clear_user_operation(user_id)

            # 2秒後返回設定頁面
            await asyncio.sleep(2)
            from telegram import CallbackQuery
            fake_query = CallbackQuery(
                id="fake",
                from_user=update.effective_user,
                chat_instance="fake",
                data="strategy_settings"
            )
            fake_update = Update(update_id=0, callback_query=fake_query)
            await self.handle_strategy_settings(fake_update, None)

        except ValueError:
            await update.message.reply_text("❌ 請輸入有效的數字 (1-1000)：")

    async def _handle_copy_trader_address_input(self, update: Update, text: str, user_id: int) -> None:
        """處理跟單錢包地址輸入"""
        try:
            # 驗證錢包地址格式
            if not text.startswith("0x") or len(text) != 42:
                await update.message.reply_text("❌ 錢包地址格式不正確，請輸入0x開頭的42位元地址：")
                return

            # 檢查地址是否已存在
            copy_traders = self.session_manager.get_user_setting(user_id, "copy_traders", [])
            if any(trader.get("address") == text.lower() for trader in copy_traders):
                await update.message.reply_text("❌ 此錢包地址已在跟單列表中，請輸入其他地址：")
                return

            # 暫存地址，等待用戶設定暱稱
            self.session_manager.set_user_operation(user_id, "setting_copy_trader_nickname", {"address": text.lower()})

            await update.message.reply_text(
                f"✅ **錢包地址驗證成功**\n\n"
                f"📍 地址：`{text}`\n\n"
                f"請為此跟單交易者設定一個暱稱（例如：高手A、策略大師等）：",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"處理跟單地址輸入失敗: {e}")
            await update.message.reply_text(f"❌ 處理失敗: {str(e)}")
            self.session_manager.clear_user_operation(user_id)

    async def _handle_copy_percentage_input(self, update: Update, text: str, user_id: int) -> None:
        """處理跟單比例輸入"""
        try:
            percentage = float(text)
            if not (1 <= percentage <= 100):
                await update.message.reply_text("❌ 跟單比例必須在 1-100 之間，請重新輸入：")
                return

            # 更新用戶設定
            self.session_manager.update_user_setting(user_id, "copy_settings.default_copy_percentage", percentage)

            await update.message.reply_text(
                f"✅ **跟單比例設定成功**\n\n預設跟單比例已設定為：`{percentage}%`\n\n正在返回設定頁面...",
                parse_mode=ParseMode.MARKDOWN
            )

            # 清除操作狀態
            self.session_manager.clear_user_operation(user_id)

            # 2秒後返回設定頁面
            await asyncio.sleep(2)
            from telegram import CallbackQuery
            fake_query = CallbackQuery(
                id="fake",
                from_user=update.effective_user,
                chat_instance="fake",
                data="copy_trader_settings"
            )
            fake_update = Update(update_id=0, callback_query=fake_query)
            await self.handle_copy_trader_settings(fake_update, None)

        except ValueError:
            await update.message.reply_text("❌ 請輸入有效的數字 (1-100)：")

    async def _handle_copy_amount_input(self, update: Update, text: str, user_id: int) -> None:
        """處理固定跟單金額輸入"""
        try:
            amount = float(text)
            if not (1 <= amount <= 10000):
                await update.message.reply_text("❌ 跟單金額必須在 1-10000 USDC 之間，請重新輸入：")
                return

            # 更新用戶設定
            self.session_manager.update_user_setting(user_id, "copy_settings.default_copy_amount", amount)

            await update.message.reply_text(
                f"✅ **固定跟單金額設定成功**\n\n預設跟單金額已設定為：`${amount}` USDC\n\n正在返回設定頁面...",
                parse_mode=ParseMode.MARKDOWN
            )

            # 清除操作狀態
            self.session_manager.clear_user_operation(user_id)

            # 2秒後返回設定頁面
            await asyncio.sleep(2)
            from telegram import CallbackQuery
            fake_query = CallbackQuery(
                id="fake",
                from_user=update.effective_user,
                chat_instance="fake",
                data="copy_trader_settings"
            )
            fake_update = Update(update_id=0, callback_query=fake_query)
            await self.handle_copy_trader_settings(fake_update, None)

        except ValueError:
            await update.message.reply_text("❌ 請輸入有效的數字 (1-10000)：")

    def get_handlers(self) -> list:
        """獲取所有處理器列表"""
        return [
            # 命令处理器
            CommandHandler("start", self.start_command),
            CommandHandler("status", self.status_command),
            CommandHandler("balance", self.balance_command),
            CommandHandler("mode", self.mode_command),
            CommandHandler("help", self.help_command),

            # 消息處理器 - 處理用戶輸入的策略參數
            MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_text_input),
            
            # 回调查询处理器
            CallbackQueryHandler(self.handle_confirmation, pattern="^(confirm_|cancel_)"),
            CallbackQueryHandler(self.handle_mode_selection, pattern="^mode_"),
            CallbackQueryHandler(self.handle_mode_menu, pattern="^mode$|^mode_menu$"),
            CallbackQueryHandler(self.handle_auto_authorization, pattern="^auto_auth_"),
            CallbackQueryHandler(self.handle_status_callback, pattern="^status$"),
            CallbackQueryHandler(self.handle_balance_callback, pattern="^balance$"),

            # 新UI處理器 - 按照UI_spec.md實現
            CallbackQueryHandler(self.handle_my_wallet, pattern="^my_wallet$"),
            CallbackQueryHandler(self.handle_strategy_bot, pattern="^strategy_bot$"),
            CallbackQueryHandler(self.handle_strategy_settings, pattern="^strategy_settings$"),
            CallbackQueryHandler(self.handle_copy_bot, pattern="^copy_bot$"),

            # 策略設定處理器
            CallbackQueryHandler(self.handle_set_win_rate, pattern="^set_win_rate$"),
            CallbackQueryHandler(self.handle_set_expiry_days, pattern="^set_expiry_days$"),
            CallbackQueryHandler(self.handle_set_trade_amount, pattern="^set_trade_amount$"),
            CallbackQueryHandler(self.handle_reset_strategy_defaults, pattern="^reset_strategy_defaults$"),
            CallbackQueryHandler(self.handle_save_strategy_settings, pattern="^save_strategy_settings$"),

            # 跟單設定處理器
            CallbackQueryHandler(self.handle_add_copy_trader, pattern="^add_copy_trader$"),
            CallbackQueryHandler(self.handle_copy_trader_settings, pattern="^copy_trader_settings$"),
            CallbackQueryHandler(self.handle_set_copy_mode, pattern="^set_copy_mode$"),
            CallbackQueryHandler(self.handle_set_copy_percentage, pattern="^set_copy_percentage$"),
            CallbackQueryHandler(self.handle_set_copy_amount, pattern="^set_copy_amount$"),
            
            # 第一批：基础导航和菜单功能
            CallbackQueryHandler(self.handle_back_navigation, pattern="^back$"),
            CallbackQueryHandler(self.handle_main_menu, pattern="^main_menu$"),
            CallbackQueryHandler(self.handle_settings_menu, pattern="^settings$"),
            CallbackQueryHandler(self.handle_history_menu, pattern="^history$"),
            CallbackQueryHandler(self.handle_trading_data_menu, pattern="^trading_data$"),
            CallbackQueryHandler(self.handle_page_info, pattern="^page_info$"),
            
            # 第二批：核心交易功能 - 紧急控制
            CallbackQueryHandler(self.handle_emergency_stop, pattern="^emergency_stop$"),
            CallbackQueryHandler(self.handle_emergency_stop_all, pattern="^emergency_stop_all$"),
            CallbackQueryHandler(self.handle_emergency_risk_protection, pattern="^emergency_risk_protection$"),
            CallbackQueryHandler(self.handle_emergency_lock_wallet, pattern="^emergency_lock_wallet$"),
            CallbackQueryHandler(self.handle_emergency_contact_admin, pattern="^emergency_contact_admin$"),
            CallbackQueryHandler(self.handle_emergency_export_logs, pattern="^emergency_export_logs$"),

            # 通用回調處理器（捕獲所有其他回調）
            CallbackQueryHandler(self._handle_unimplemented_callback, pattern="^.*$"),
        ]
