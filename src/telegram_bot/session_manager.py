#!/usr/bin/env python3
"""
多用戶會話管理系統
管理不同 Telegram 用戶的會話狀態和數據隔離
"""

import asyncio
from typing import Dict, Any, Optional, Set, List
from datetime import datetime, timedelta
from dataclasses import dataclass, field

from src.core.config import Config
from src.bot_controller import BotController, TradingMode
from src.utils.logging_config import get_logger
from .user_wallet_manager import UserWalletManager


@dataclass
class UserSession:
    """用戶會話數據類"""
    user_id: int
    username: str
    chat_id: int
    
    # 授權狀態
    is_authorized: bool = False
    authorization_level: str = "basic"  # basic, admin, superuser
    
    # 會話狀態
    session_start_time: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    
    # 用戶配置
    trading_mode: TradingMode = TradingMode.DISABLED
    notifications_enabled: bool = True
    
    # 用戶專屬設置
    user_settings: Dict[str, Any] = field(default_factory=dict)
    
    # 會話狀態
    current_operation: Optional[str] = None
    waiting_for_input: bool = False
    input_context: Dict[str, Any] = field(default_factory=dict)
    
    # 用戶錢包信息（不存儲私鑰）
    wallet_address: Optional[str] = None
    has_wallet: bool = False


class SessionManager:
    """多用戶會話管理器"""
    
    def __init__(self, config: Config):
        """初始化會話管理器
        
        Args:
            config: 系統配置
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 用戶會話存儲
        self.sessions: Dict[int, UserSession] = {}
        
        # 用戶錢包管理器
        self.user_wallet_manager = UserWalletManager(config)
        
        # 授權用戶列表（從配置或數據庫加載）
        self.authorized_users: Set[int] = set()
        self.admin_users: Set[int] = set()
        
        # 會話過期時間（24小時）
        self.session_timeout = timedelta(hours=24)
        
        # 定時清理任務
        self._cleanup_task: Optional[asyncio.Task] = None
        
        self.logger.info("會話管理器初始化完成")
    
    async def start(self) -> None:
        """啟動會話管理器"""
        # 加載授權用戶配置
        await self._load_authorized_users()
        
        # 啟動定時清理任務
        self._cleanup_task = asyncio.create_task(self._cleanup_expired_sessions())
        
        self.logger.info("✅ 會話管理器已啟動")
    
    async def stop(self) -> None:
        """停止會話管理器"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("✅ 會話管理器已停止")
    
    async def _load_authorized_users(self) -> None:
        """加載授權用戶列表"""
        # 從環境變數或配置文件加載授權用戶
        import os
        
        # 檢查是否啟用自動授權模式
        auto_auth = os.getenv("TELEGRAM_AUTO_AUTHORIZATION", "true").lower() == "true"
        
        if auto_auth:
            self.logger.info("🚀 自動授權模式已啟用 - 新用戶將自動獲得基本授權")
        else:
            # 傳統模式：從環境變數加載授權用戶
            authorized_str = os.getenv("TELEGRAM_AUTHORIZED_USERS", "")
            if authorized_str:
                try:
                    user_ids = [int(uid.strip()) for uid in authorized_str.split(",") if uid.strip()]
                    self.authorized_users.update(user_ids)
                    self.logger.info(f"已加載 {len(user_ids)} 個授權用戶")
                except ValueError as e:
                    self.logger.error(f"解析授權用戶列表失敗: {e}")
        
        # 管理員用戶（始終從環境變數加載）
        admin_str = os.getenv("TELEGRAM_ADMIN_USERS", "")
        if admin_str:
            try:
                admin_ids = [int(uid.strip()) for uid in admin_str.split(",") if uid.strip()]
                self.admin_users.update(admin_ids)
                self.authorized_users.update(admin_ids)  # 管理員自動授權
                self.logger.info(f"已加載 {len(admin_ids)} 個管理員用戶")
            except ValueError as e:
                self.logger.error(f"解析管理員用戶列表失敗: {e}")
    
    def get_or_create_session(self, user_id: int, username: str, chat_id: int) -> UserSession:
        """獲取或創建用戶會話
        
        Args:
            user_id: Telegram 用戶 ID
            username: 用戶名
            chat_id: 聊天 ID
        
        Returns:
            用戶會話對象
        """
        if user_id not in self.sessions:
            # 檢查是否啟用自動授權
            import os
            auto_auth = os.getenv("TELEGRAM_AUTO_AUTHORIZATION", "true").lower() == "true"
            
            # 確定授權狀態
            is_authorized = (
                user_id in self.authorized_users or  # 已在授權列表中
                user_id in self.admin_users or      # 是管理員
                auto_auth                            # 自動授權模式
            )
            
            # 創建新會話
            session = UserSession(
                user_id=user_id,
                username=username,
                chat_id=chat_id,
                is_authorized=is_authorized,
                authorization_level="admin" if user_id in self.admin_users else "basic"
            )
            
            # 設置默認用戶設置
            session.user_settings = {
                "language": "zh",
                "timezone": "Asia/Taipei",
                "notification_types": {
                    "trade_executed": True,
                    "trade_failed": True,
                    "balance_warning": True,
                    "daily_summary": False
                },
                "risk_settings": {
                    "max_trade_amount": 100.0,
                    "max_slippage": 0.05,
                    "enable_stop_loss": True
                },
                # 新增：用戶自定義策略設定
                "strategy_settings": {
                    "min_win_rate": 85.0,      # 最低勝率閾值 (%)
                    "max_expiry_days": 1,      # 最長到期天數
                    "trade_amount": 10.0       # 固定下單金額 (USDC)
                },
                # 新增：跟單設定
                "copy_traders": []  # 跟單交易員列表
            }
            
            self.sessions[user_id] = session
            
            # 記錄新用戶會話創建
            auth_status = "管理員" if user_id in self.admin_users else ("已授權" if is_authorized else "未授權")
            self.logger.info(f"為用戶 {username} ({user_id}) 創建新會話 - 狀態: {auth_status}")
            
            # 如果是自動授權的新用戶，添加到授權列表
            import os
            auto_auth = os.getenv("TELEGRAM_AUTO_AUTHORIZATION", "true").lower() == "true"
            if auto_auth and is_authorized and user_id not in self.authorized_users and user_id not in self.admin_users:
                self.authorized_users.add(user_id)
                self.logger.info(f"✅ 用戶 {username} ({user_id}) 通過自動授權系統獲得授權")
        else:
            # 更新現有會話
            session = self.sessions[user_id]
            session.last_activity = datetime.now()
            session.username = username  # 更新用戶名（可能變更）
            session.chat_id = chat_id    # 更新聊天 ID
        
        return self.sessions[user_id]
    
    def get_session(self, user_id: int) -> Optional[UserSession]:
        """獲取用戶會話
        
        Args:
            user_id: 用戶 ID
        
        Returns:
            用戶會話對象或 None
        """
        return self.sessions.get(user_id)
    
    def is_authorized(self, user_id: int) -> bool:
        """檢查用戶是否已授權
        
        Args:
            user_id: 用戶 ID
        
        Returns:
            是否已授權
        """
        session = self.get_session(user_id)
        return session and session.is_authorized
    
    def is_admin(self, user_id: int) -> bool:
        """檢查用戶是否為管理員
        
        Args:
            user_id: 用戶 ID
        
        Returns:
            是否為管理員
        """
        return user_id in self.admin_users
    
    def update_user_setting(self, user_id: int, key: str, value: Any) -> bool:
        """更新用戶設置
        
        Args:
            user_id: 用戶 ID
            key: 設置鍵
            value: 設置值
        
        Returns:
            是否更新成功
        """
        session = self.get_session(user_id)
        if not session:
            return False
        
        session.user_settings[key] = value
        session.last_activity = datetime.now()
        
        self.logger.info(f"用戶 {user_id} 更新設置: {key} = {value}")
        return True
    
    def get_user_setting(self, user_id: int, key: str, default: Any = None) -> Any:
        """獲取用戶設置
        
        Args:
            user_id: 用戶 ID
            key: 設置鍵
            default: 默認值
        
        Returns:
            設置值
        """
        session = self.get_session(user_id)
        if not session:
            return default
        
        return session.user_settings.get(key, default)

    def update_user_strategy_setting(self, user_id: int, key: str, value: Any) -> bool:
        """更新用戶策略設定

        Args:
            user_id: 用戶 ID
            key: 策略設定鍵 (min_win_rate, max_expiry_days, trade_amount)
            value: 設定值

        Returns:
            是否更新成功
        """
        session = self.get_session(user_id)
        if not session:
            return False

        if "strategy_settings" not in session.user_settings:
            session.user_settings["strategy_settings"] = {
                "min_win_rate": 85.0,
                "max_expiry_days": 1,
                "trade_amount": 10.0
            }

        session.user_settings["strategy_settings"][key] = value
        session.last_activity = datetime.now()

        self.logger.info(f"用戶 {user_id} 更新策略設定: {key} = {value}")
        return True

    def get_user_strategy_settings(self, user_id: int) -> Dict[str, Any]:
        """獲取用戶策略設定

        Args:
            user_id: 用戶 ID

        Returns:
            策略設定字典
        """
        session = self.get_session(user_id)
        if not session:
            return {
                "min_win_rate": 85.0,
                "max_expiry_days": 1,
                "trade_amount": 10.0
            }

        return session.user_settings.get("strategy_settings", {
            "min_win_rate": 85.0,
            "max_expiry_days": 1,
            "trade_amount": 10.0
        })

    def reset_user_strategy_settings(self, user_id: int) -> bool:
        """重置用戶策略設定為預設值

        Args:
            user_id: 用戶 ID

        Returns:
            是否重置成功
        """
        session = self.get_session(user_id)
        if not session:
            return False

        session.user_settings["strategy_settings"] = {
            "min_win_rate": 85.0,
            "max_expiry_days": 1,
            "trade_amount": 10.0
        }
        session.last_activity = datetime.now()

        self.logger.info(f"用戶 {user_id} 重置策略設定為預設值")
        return True

    def set_user_operation(self, user_id: int, operation: str, context: Dict[str, Any] = None) -> bool:
        """設置用戶當前操作狀態
        
        Args:
            user_id: 用戶 ID
            operation: 操作名稱
            context: 操作上下文
        
        Returns:
            是否設置成功
        """
        session = self.get_session(user_id)
        if not session:
            return False
        
        session.current_operation = operation
        session.waiting_for_input = True
        session.input_context = context or {}
        session.last_activity = datetime.now()
        
        return True
    
    def clear_user_operation(self, user_id: int) -> bool:
        """清除用戶操作狀態
        
        Args:
            user_id: 用戶 ID
        
        Returns:
            是否清除成功
        """
        session = self.get_session(user_id)
        if not session:
            return False
        
        session.current_operation = None
        session.waiting_for_input = False
        session.input_context = {}
        session.last_activity = datetime.now()
        
        return True
    
    def get_active_users(self) -> List[UserSession]:
        """獲取活躍用戶列表
        
        Returns:
            活躍用戶會話列表
        """
        now = datetime.now()
        active_sessions = []
        
        for session in self.sessions.values():
            if now - session.last_activity < self.session_timeout:
                active_sessions.append(session)
        
        return active_sessions
    
    def get_authorized_users(self) -> List[UserSession]:
        """獲取已授權用戶列表
        
        Returns:
            已授權用戶會話列表
        """
        return [session for session in self.sessions.values() if session.is_authorized]
    
    async def authorize_user(self, user_id: int, admin_user_id: int) -> bool:
        """授權用戶
        
        Args:
            user_id: 要授權的用戶 ID
            admin_user_id: 執行授權的管理員 ID
        
        Returns:
            是否授權成功
        """
        if not self.is_admin(admin_user_id):
            self.logger.warning(f"非管理員用戶 {admin_user_id} 嘗試授權用戶 {user_id}")
            return False
        
        session = self.get_session(user_id)
        if session:
            session.is_authorized = True
            self.authorized_users.add(user_id)
        
        self.logger.info(f"管理員 {admin_user_id} 授權用戶 {user_id}")
        return True
    
    async def revoke_user(self, user_id: int, admin_user_id: int) -> bool:
        """撤銷用戶授權
        
        Args:
            user_id: 要撤銷的用戶 ID
            admin_user_id: 執行撤銷的管理員 ID
        
        Returns:
            是否撤銷成功
        """
        if not self.is_admin(admin_user_id):
            self.logger.warning(f"非管理員用戶 {admin_user_id} 嘗試撤銷用戶 {user_id}")
            return False
        
        session = self.get_session(user_id)
        if session:
            session.is_authorized = False
            self.authorized_users.discard(user_id)
        
        self.logger.info(f"管理員 {admin_user_id} 撤銷用戶 {user_id} 授權")
        return True
    
    def is_auto_authorization_enabled(self) -> bool:
        """檢查是否啟用自動授權
        
        Returns:
            是否啟用自動授權
        """
        import os
        return os.getenv("TELEGRAM_AUTO_AUTHORIZATION", "true").lower() == "true"
    
    async def complete_auto_authorization(self, user_id: int, risk_acknowledged: bool = False) -> bool:
        """完成自動授權流程
        
        Args:
            user_id: 用戶 ID
            risk_acknowledged: 是否已確認風險
        
        Returns:
            是否授權成功
        """
        if not self.is_auto_authorization_enabled():
            self.logger.warning(f"用戶 {user_id} 嘗試自動授權，但自動授權功能已禁用")
            return False
        
        session = self.get_session(user_id)
        if not session:
            self.logger.error(f"用戶 {user_id} 會話不存在，無法完成授權")
            return False
        
        if session.is_authorized:
            self.logger.info(f"用戶 {user_id} 已經是授權用戶")
            return True
        
        if not risk_acknowledged:
            self.logger.warning(f"用戶 {user_id} 未確認風險，無法完成自動授權")
            return False
        
        # 完成自動授權
        session.is_authorized = True
        self.authorized_users.add(user_id)
        
        # 記錄授權完成
        self.logger.info(f"🎉 用戶 {session.username} ({user_id}) 完成自動授權流程")
        
        return True
    
    async def _cleanup_expired_sessions(self) -> None:
        """定時清理過期會話"""
        while True:
            try:
                await asyncio.sleep(3600)  # 每小時清理一次
                
                now = datetime.now()
                expired_users = []
                
                for user_id, session in self.sessions.items():
                    if now - session.last_activity > self.session_timeout:
                        expired_users.append(user_id)
                
                # 清理過期會話
                for user_id in expired_users:
                    del self.sessions[user_id]
                    self.logger.info(f"清理過期會話: 用戶 {user_id}")
                
                if expired_users:
                    self.logger.info(f"清理了 {len(expired_users)} 個過期會話")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"會話清理任務錯誤: {e}")
                await asyncio.sleep(300)  # 出錯後等待5分鐘
    
    def get_session_stats(self) -> Dict[str, Any]:
        """獲取會話統計信息
        
        Returns:
            統計信息字典
        """
        active_sessions = self.get_active_users()
        authorized_sessions = self.get_authorized_users()
        
        return {
            "total_sessions": len(self.sessions),
            "active_sessions": len(active_sessions),
            "authorized_sessions": len(authorized_sessions),
            "admin_users": len(self.admin_users),
            "session_timeout_hours": self.session_timeout.total_seconds() / 3600
        }
    
    # 用戶錢包管理方法
    
    def get_user_wallet_address(self, user_id: int) -> Optional[str]:
        """獲取用戶錢包地址
        
        Args:
            user_id: 用戶 ID
        
        Returns:
            錢包地址或 None
        """
        session = self.get_session(user_id)
        return session.wallet_address if session else None
    
    def get_user_balance(self, user_id: int) -> Dict[str, Any]:
        """獲取用戶錢包餘額
        
        Args:
            user_id: 用戶 ID
        
        Returns:
            餘額信息字典
        """
        session = self.get_session(user_id)
        if not session or not session.has_wallet:
            return {'MATIC': 0, 'USDC': 0, 'error': '錢包不存在'}
        
        return self.user_wallet_manager.get_user_balance(user_id)
    
    def ensure_user_wallet(self, user_id: int, username: str) -> bool:
        """確保用戶擁有錢包
        
        Args:
            user_id: 用戶 ID
            username: 用戶名
        
        Returns:
            是否成功創建或已存在錢包
        """
        session = self.get_session(user_id)
        if not session:
            return False
        
        if session.has_wallet:
            return True
        
        if not session.is_authorized:
            return False
        
        try:
            user_wallet = self.user_wallet_manager.create_user_wallet(user_id, username)
            session.wallet_address = user_wallet.wallet_address
            session.has_wallet = True
            self.logger.info(f"為用戶 {username} ({user_id}) 創建錢包: {user_wallet.wallet_address}")
            return True
        except Exception as e:
            self.logger.error(f"為用戶 {user_id} 創建錢包失敗: {e}")
            return False
    
    def get_wallet_creation_guide(self, user_id: int) -> Optional[str]:
        """獲取錢包創建指導
        
        Args:
            user_id: 用戶 ID
        
        Returns:
            指導信息或 None
        """
        session = self.get_session(user_id)
        if not session or not session.wallet_address:
            return None
        
        return self.user_wallet_manager.get_wallet_creation_guide(user_id, session.wallet_address)

    def lock_user_wallet(self, user_id: int) -> bool:
        """鎖定用戶錢包

        Args:
            user_id: 用戶 ID

        Returns:
            是否鎖定成功
        """
        session = self.get_session(user_id)
        if not session:
            return False

        try:
            # 設置錢包為鎖定狀態
            session.user_settings['wallet_locked'] = True
            session.user_settings['lock_timestamp'] = datetime.now().isoformat()

            self.logger.warning(f"用戶 {user_id} 的錢包已被鎖定")
            return True

        except Exception as e:
            self.logger.error(f"鎖定用戶 {user_id} 錢包失敗: {e}")
            return False

    def unlock_user_wallet(self, user_id: int) -> bool:
        """解鎖用戶錢包

        Args:
            user_id: 用戶 ID

        Returns:
            是否解鎖成功
        """
        session = self.get_session(user_id)
        if not session:
            return False

        try:
            # 移除鎖定狀態
            session.user_settings.pop('wallet_locked', None)
            session.user_settings.pop('lock_timestamp', None)

            self.logger.info(f"用戶 {user_id} 的錢包已解鎖")
            return True

        except Exception as e:
            self.logger.error(f"解鎖用戶 {user_id} 錢包失敗: {e}")
            return False

    def is_wallet_locked(self, user_id: int) -> bool:
        """檢查用戶錢包是否被鎖定

        Args:
            user_id: 用戶 ID

        Returns:
            是否被鎖定
        """
        session = self.get_session(user_id)
        if not session:
            return False

        return session.user_settings.get('wallet_locked', False)