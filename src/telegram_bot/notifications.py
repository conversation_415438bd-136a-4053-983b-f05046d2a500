#!/usr/bin/env python3
"""
Telegram Bot 通知系統模組
處理所有類型的通知消息發送
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
from telegram import Bo<PERSON>
from telegram.constants import ParseMode
from telegram.error import TelegramError

from src.core.config import Config
from src.utils.logging_config import get_logger


class NotificationManager:
    """通知管理器類"""
    
    def __init__(self, config: Config):
        """初始化通知管理器
        
        Args:
            config: 系統配置
        """
        self.config = config
        self.logger = get_logger(__name__)
        self.bot = Bot(token=config.telegram_bot_token)
        
        # 通知設置
        self.enabled_notifications = {
            'trade_executed': True,
            'trade_failed': True,
            'mode_changed': True,
            'error_occurred': True,
            'daily_summary': True,
            'wallet_balance_low': True,
            'high_profit_trade': True,
            'system_status': True
        }
        
        # 用戶列表（實際應從數據庫獲取）
        self.notification_users: List[int] = []
    
    async def add_notification_user(self, user_id: int) -> bool:
        """添加通知用戶
        
        Args:
            user_id: 用戶 ID
        
        Returns:
            是否添加成功
        """
        try:
            if user_id not in self.notification_users:
                self.notification_users.append(user_id)
                self.logger.info(f"已添加通知用戶: {user_id}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"添加通知用戶失敗: {e}")
            return False
    
    async def remove_notification_user(self, user_id: int) -> bool:
        """移除通知用戶
        
        Args:
            user_id: 用戶 ID
        
        Returns:
            是否移除成功
        """
        try:
            if user_id in self.notification_users:
                self.notification_users.remove(user_id)
                self.logger.info(f"已移除通知用戶: {user_id}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"移除通知用戶失敗: {e}")
            return False
    
    async def send_trade_notification(
        self,
        trade_type: str,
        success: bool,
        details: Dict[str, Any]
    ) -> None:
        """發送交易通知
        
        Args:
            trade_type: 交易類型 ('copy' 或 'strategy')
            success: 是否成功
            details: 交易詳情
        """
        if not self.enabled_notifications.get('trade_executed' if success else 'trade_failed'):
            return
        
        try:
            status_emoji = "✅" if success else "❌"
            status_text = "成功" if success else "失敗"
            trade_emoji = "🎯" if trade_type == "copy" else "📈"
            trade_name = "跟單交易" if trade_type == "copy" else "策略交易"
            
            message = f"""
{status_emoji} **{trade_name}{status_text}**

**交易信息：**
{trade_emoji} 類型：{trade_name}
💰 金額：${details.get('amount', 'N/A')}
🎲 市場：{details.get('market_title', 'N/A')[:50]}...
📊 結果：{details.get('outcome', 'N/A')}
⏰ 時間：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**執行詳情：**
🔗 交易 Hash：`{details.get('tx_hash', 'N/A')[:20]}...`
⛽ Gas 費用：{details.get('gas_fee', 'N/A')} MATIC
📈 滑點：{details.get('slippage', 'N/A')}% 
"""
            
            if not success and details.get('error'):
                message += f"\n❌ **錯誤原因：**\n`{details['error']}`"
            
            if success and details.get('profit_loss'):
                pnl = details['profit_loss']
                pnl_emoji = "📈" if pnl >= 0 else "📉"
                message += f"\n{pnl_emoji} **預期 PnL：** ${pnl:.2f}"
            
            await self._send_to_all_users(message, ParseMode.MARKDOWN)
            
        except Exception as e:
            self.logger.error(f"發送交易通知失敗: {e}")

    async def send_trade_failure_notification(self, user_id: int, details: Dict[str, Any]) -> None:
        """發送交易失敗通知給特定用戶

        Args:
            user_id: 用戶 ID
            details: 交易詳情
        """
        if not self.enabled_notifications.get('trade_failed', True):
            return

        try:
            trade_type = details.get('trade_type', 'strategy')
            trade_emoji = "🎯" if trade_type == "copy" else "📈"
            trade_name = "跟單交易" if trade_type == "copy" else "策略交易"
            error_message = details.get('error', '未知錯誤')

            # 對常見錯誤進行人性化解釋
            if "insufficient funds" in error_message.lower():
                error_reason = "錢包餘額不足，請檢查您的 USDC 和 MATIC 餘額。"
            elif "transaction failed" in error_message.lower():
                error_reason = "交易在鏈上執行失敗，可能由於網路擁堵或智能合約問題。"
            else:
                error_reason = error_message

            message = f"""
❌ **{trade_name} 執行失敗**

很抱歉，系統在嘗試為您執行一筆交易時遇到問題。

**失敗詳情：**
{trade_emoji} 類型：{trade_name}
💰 嘗試金額：${details.get('amount', 'N/A')}
🎲 市場：{details.get('market_title', 'N/A')[:50]}...

**可能原因：**
`{error_reason}`

**建議操作：**
1.  使用 `/balance` 命令檢查您的錢包餘額。
2.  確保您有足夠的 MATIC 支付 Gas 費用。
3.  如果問題持續，請考慮暫時停止機器人並聯繫管理員。
"""
            await self._send_to_user(user_id, message, ParseMode.MARKDOWN)
            self.logger.warning(f"已向用戶 {user_id} 發送交易失敗通知: {error_reason}")

        except Exception as e:
            self.logger.error(f"發送交易失敗通知給 {user_id} 失敗: {e}")
    
    async def send_mode_change_notification(self, user_id: int, new_mode) -> None:
        """發送模式切換通知
        
        Args:
            user_id: 用戶 ID
            new_mode: 新的交易模式
        """
        if not self.enabled_notifications.get('mode_changed'):
            return
        
        try:
            mode_emojis = {
                TradingMode.COPY_TRADING: "🎯",
                TradingMode.STRATEGY_TRADING: "📈",
                TradingMode.MIXED: "🔀",
                TradingMode.DISABLED: "🛑"
            }
            
            mode_names = {
                TradingMode.COPY_TRADING: "V2 跟單模式",
                TradingMode.STRATEGY_TRADING: "V1 策略模式",
                TradingMode.MIXED: "混合模式",
                TradingMode.DISABLED: "停用模式"
            }
            
            emoji = mode_emojis.get(new_mode, "❓")
            name = mode_names.get(new_mode, "未知模式")
            
            message = f"""
🔄 **交易模式已切換**

{emoji} **新模式：** {name}
⏰ **切換時間：** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
👤 **操作用戶：** {user_id}

**模式說明：**
"""
            
            # 動態導入避免循環導入
            from src.bot_controller import TradingMode

            if new_mode == TradingMode.COPY_TRADING:
                message += "跟隨聰明錢錢包自動執行交易"
            elif new_mode == TradingMode.STRATEGY_TRADING:
                message += "基於預設條件自主分析和交易"
            elif new_mode == TradingMode.MIXED:
                message += "同時運行跟單和策略模式"
            else:
                message += "所有自動交易功能已停用"
            
            await self._send_to_user(user_id, message, ParseMode.MARKDOWN)
            
        except Exception as e:
            self.logger.error(f"發送模式切換通知失敗: {e}")
    
    async def send_error_notification(self, error_type: str, error_message: str, context: Dict[str, Any] = None) -> None:
        """發送錯誤通知
        
        Args:
            error_type: 錯誤類型
            error_message: 錯誤消息
            context: 錯誤上下文
        """
        if not self.enabled_notifications.get('error_occurred'):
            return
        
        try:
            context = context or {}
            
            message = f"""
🚨 **系統錯誤警告**

⚠️ **錯誤類型：** {error_type}
📝 **錯誤描述：** {error_message[:200]}...
⏰ **發生時間：** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**錯誤上下文：**
"""
            
            if context.get('module'):
                message += f"📦 模塊：{context['module']}\n"
            if context.get('function'):
                message += f"🔧 函數：{context['function']}\n"
            if context.get('user_id'):
                message += f"👤 用戶：{context['user_id']}\n"
            
            message += "\n**建議操作：**\n"
            message += "• 檢查系統日誌獲取詳細信息\n"
            message += "• 確認網路連接和 API 狀態\n"
            message += "• 考慮重啟相關模塊\n"
            
            await self._send_to_all_users(message, ParseMode.MARKDOWN)
            
        except Exception as e:
            self.logger.error(f"發送錯誤通知失敗: {e}")
    
    async def send_balance_warning(self, balance_info: Dict[str, float]) -> None:
        """發送餘額不足警告
        
        Args:
            balance_info: 餘額信息
        """
        if not self.enabled_notifications.get('wallet_balance_low'):
            return
        
        try:
            usdc_balance = balance_info.get('usdc', 0)
            matic_balance = balance_info.get('matic', 0)
            
            message = f"""
⚠️ **錢包餘額警告**

💰 **當前餘額：**
💵 USDC：${usdc_balance:.2f}
⛽ MATIC：{matic_balance:.4f}

**警告原因：**
"""
            
            warnings = []
            if usdc_balance < 10:
                warnings.append("• USDC 餘額不足，可能影響交易執行")
            if matic_balance < 0.1:
                warnings.append("• MATIC 餘額不足，可能無法支付 Gas 費用")
            
            message += "\n".join(warnings)
            
            message += """

**建議操作：**
• 及時充值 USDC 和 MATIC
• 檢查錢包地址是否正確
• 暫停自動交易直到充值完成
"""
            
            await self._send_to_all_users(message, ParseMode.MARKDOWN)
            
        except Exception as e:
            self.logger.error(f"發送餘額警告失敗: {e}")
    
    async def send_daily_summary(self, summary_data: Dict[str, Any]) -> None:
        """發送日報摘要
        
        Args:
            summary_data: 日報數據
        """
        if not self.enabled_notifications.get('daily_summary'):
            return
        
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            
            message = f"""
📊 **每日交易報告 - {today}**

**交易統計：**
📈 總交易數：{summary_data.get('total_trades', 0)}
✅ 成功交易：{summary_data.get('successful_trades', 0)}
❌ 失敗交易：{summary_data.get('failed_trades', 0)}
📊 成功率：{summary_data.get('success_rate', 0):.1f}%

**財務表現：**
💰 總 PnL：${summary_data.get('total_pnl', 0):.2f}
📈 最大盈利：${summary_data.get('max_profit', 0):.2f}
📉 最大虧損：${summary_data.get('max_loss', 0):.2f}
💵 交易總額：${summary_data.get('total_volume', 0):.2f}

**模式分布：**
🎯 跟單交易：{summary_data.get('copy_trades', 0)} 筆
📈 策略交易：{summary_data.get('strategy_trades', 0)} 筆

**系統表現：**
⚡ 平均延遲：{summary_data.get('avg_latency', 0):.2f}s
⛽ 總 Gas 費用：{summary_data.get('total_gas_fee', 0):.4f} MATIC
🔄 系統正常運行時間：{summary_data.get('uptime_percentage', 0):.1f}%

祝您交易順利！🚀
"""
            
            await self._send_to_all_users(message, ParseMode.MARKDOWN)
            
        except Exception as e:
            self.logger.error(f"發送日報摘要失敗: {e}")
    
    async def send_high_profit_alert(self, trade_details: Dict[str, Any]) -> None:
        """發送高收益交易提醒
        
        Args:
            trade_details: 交易詳情
        """
        if not self.enabled_notifications.get('high_profit_trade'):
            return
        
        try:
            profit = trade_details.get('profit', 0)
            profit_percentage = trade_details.get('profit_percentage', 0)
            
            message = f"""
🎉 **高收益交易提醒**

💰 **盈利金額：** ${profit:.2f}
📊 **收益率：** {profit_percentage:.1f}%
🎲 **市場：** {trade_details.get('market_title', 'N/A')[:50]}...
🎯 **交易類型：** {trade_details.get('trade_type', 'N/A')}

**交易詳情：**
💵 投入金額：${trade_details.get('amount', 0):.2f}
📈 結果選擇：{trade_details.get('outcome', 'N/A')}
⏰ 執行時間：{trade_details.get('timestamp', 'N/A')}

恭喜您的成功交易！🚀
"""
            
            await self._send_to_all_users(message, ParseMode.MARKDOWN)
            
        except Exception as e:
            self.logger.error(f"發送高收益提醒失敗: {e}")
    
    async def send_system_status_update(self, status_data: Dict[str, Any]) -> None:
        """發送系統狀態更新
        
        Args:
            status_data: 狀態數據
        """
        if not self.enabled_notifications.get('system_status'):
            return
        
        try:
            is_healthy = status_data.get('is_healthy', False)
            status_emoji = "🟢" if is_healthy else "🔴"
            status_text = "正常" if is_healthy else "異常"
            
            message = f"""
{status_emoji} **系統狀態更新**

**整體狀態：** {status_text}
**運行模式：** {status_data.get('current_mode', 'N/A')}
**運行時間：** {status_data.get('uptime', 'N/A')}

**模塊狀態：**
🎯 跟單系統：{self._format_status(status_data.get('copy_system_status'))}
📈 策略系統：{self._format_status(status_data.get('strategy_system_status'))}
💾 數據庫：{self._format_status(status_data.get('database_status'))}
🌐 網路連接：{self._format_status(status_data.get('network_status'))}

**性能指標：**
📊 CPU 使用率：{status_data.get('cpu_usage', 0):.1f}%
💾 內存使用率：{status_data.get('memory_usage', 0):.1f}%
🌐 API 響應時間：{status_data.get('api_latency', 0):.2f}s
"""
            
            await self._send_to_all_users(message, ParseMode.MARKDOWN)
            
        except Exception as e:
            self.logger.error(f"發送系統狀態更新失敗: {e}")
    
    async def _send_to_user(self, user_id: int, message: str, parse_mode: ParseMode = None) -> bool:
        """發送消息給指定用戶
        
        Args:
            user_id: 用戶 ID
            message: 消息內容
            parse_mode: 解析模式
        
        Returns:
            是否發送成功
        """
        try:
            await self.bot.send_message(
                chat_id=user_id,
                text=message,
                parse_mode=parse_mode
            )
            return True
            
        except TelegramError as e:
            self.logger.warning(f"發送消息給用戶 {user_id} 失敗: {e}")
            return False
        except Exception as e:
            self.logger.error(f"發送消息時發生未知錯誤: {e}")
            return False
    
    async def _send_to_all_users(self, message: str, parse_mode: ParseMode = None) -> int:
        """發送消息給所有用戶
        
        Args:
            message: 消息內容
            parse_mode: 解析模式
        
        Returns:
            成功發送的用戶數量
        """
        success_count = 0
        
        for user_id in self.notification_users:
            if await self._send_to_user(user_id, message, parse_mode):
                success_count += 1
            
            # 避免觸發 Telegram 限制
            await asyncio.sleep(0.1)
        
        self.logger.info(f"通知已發送給 {success_count}/{len(self.notification_users)} 位用戶")
        return success_count
    
    def _format_status(self, status: Optional[bool]) -> str:
        """格式化狀態顯示
        
        Args:
            status: 狀態值
        
        Returns:
            格式化的狀態字符串
        """
        if status is None:
            return "❓ 未知"
        elif status:
            return "✅ 正常"
        else:
            return "❌ 異常"
    
    def enable_notification(self, notification_type: str) -> bool:
        """啟用特定類型的通知
        
        Args:
            notification_type: 通知類型
        
        Returns:
            是否操作成功
        """
        if notification_type in self.enabled_notifications:
            self.enabled_notifications[notification_type] = True
            return True
        return False
    
    def disable_notification(self, notification_type: str) -> bool:
        """禁用特定類型的通知
        
        Args:
            notification_type: 通知類型
        
        Returns:
            是否操作成功
        """
        if notification_type in self.enabled_notifications:
            self.enabled_notifications[notification_type] = False
            return True
        return False
    
    def get_notification_settings(self) -> Dict[str, bool]:
        """獲取通知設置
        
        Returns:
            通知設置字典
        """
        return self.enabled_notifications.copy()