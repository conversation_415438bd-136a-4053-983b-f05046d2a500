#!/usr/bin/env python3
"""
Telegram Bot 主要邏輯模組
整合所有 Bot 組件並提供統一的接口
"""

import asyncio
import signal
import sys
from typing import Optional, Dict, Any
from telegram.ext import Application, ApplicationBuilder, CommandHandler, CallbackQueryHandler, MessageHandler, filters
from telegram.error import TelegramError

from src.core.config import Config
from src.bot_controller import BotController
from src.database.repository import Repository
from src.web3_tools.wallet import WalletManager
from src.web3_tools.transactions import TransactionManager
from src.trading.polymarket_client import PolymarketClient
from src.utils.logging_config import get_logger

from .handlers import TelegramHandlers
from .keyboards import BotKeyboards
from .notifications import NotificationManager
from .session_manager import SessionManager


class TelegramBot:
    """Telegram Bot 主類"""
    
    def __init__(self, config: Config, bot_controller: Optional[BotController] = None):
        """初始化 Telegram Bot
        
        Args:
            config: 系統配置
            bot_controller: 機器人控制器（可選，會自動創建）
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 核心組件
        self.application: Optional[Application] = None
        self.bot_controller = bot_controller
        self.notification_manager: Optional[NotificationManager] = None
        self.session_manager: Optional[SessionManager] = None
        self.handlers: Optional[TelegramHandlers] = None
        
        # 運行狀態
        self.is_running = False
        self._shutdown_event = asyncio.Event()
        
        self.logger.info("Telegram Bot 初始化完成")
    
    async def initialize_components(self) -> None:
        """初始化所有組件"""
        try:
            self.logger.info("正在初始化 Telegram Bot 組件...")
            
            # 創建 Telegram Application
            self.application = (
                ApplicationBuilder()
                .token(self.config.telegram_bot_token)
                .connect_timeout(30)
                .read_timeout(30)
                .build()
            )
            
            # 初始化核心組件（簡化版本，實際應該從依賴注入容器獲取）
            if not self.bot_controller:
                await self._initialize_core_components()
            
            # 初始化 Bot 專用組件
            self.notification_manager = NotificationManager(self.config)
            self.session_manager = SessionManager(self.config)
            
            # 啟動會話管理器
            await self.session_manager.start()
            
            # 初始化命令處理器
            self.handlers = TelegramHandlers(
                config=self.config,
                bot_controller=self.bot_controller,
                session_manager=self.session_manager
            )
            
            # 註冊處理器
            self._register_handlers()
            
            # 設置錯誤處理器
            self.application.add_error_handler(self._error_handler)
            
            self.logger.info("✅ Telegram Bot 組件初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化組件失敗: {e}")
            raise
    
    async def _initialize_core_components(self) -> None:
        """初始化核心組件（簡化版本）"""
        try:
            # 初始化數據庫
            repository = Repository(self.config)
            
            # 初始化 Web3 組件
            wallet_manager = WalletManager(self.config)
            transaction_manager = TransactionManager(self.config, wallet_manager)
            
            # 初始化 Polymarket 客戶端
            polymarket_client = PolymarketClient(self.config, wallet_manager, transaction_manager)
            
            # 初始化 Bot 控制器
            self.bot_controller = BotController(
                config=self.config,
                repository=repository,
                wallet_manager=wallet_manager,
                transaction_manager=transaction_manager,
                polymarket_client=polymarket_client
            )
            
            # 初始化交易組件
            await self.bot_controller.initialize_components()
            
            self.logger.info("✅ 核心組件初始化完成")
            
        except Exception as e:
            self.logger.error(f"核心組件初始化失敗: {e}")
            raise
    
    def _register_handlers(self) -> None:
        """註冊所有處理器"""
        if not self.handlers:
            raise RuntimeError("Handlers not initialized")
        
        # 獲取所有處理器
        handlers = self.handlers.get_handlers()
        
        # 註冊處理器
        for handler in handlers:
            self.application.add_handler(handler)
        
        # 註冊額外的回調處理器
        self.application.add_handler(
            CallbackQueryHandler(self._handle_callback_query)
        )
        
        # 註冊文本消息處理器
        self.application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self._handle_text_message)
        )
        
        self.logger.info(f"✅ 已註冊 {len(handlers) + 2} 個處理器")
    
    async def _handle_callback_query(self, update, context) -> None:
        """處理回調查詢"""
        query = update.callback_query
        await query.answer()
        
        try:
            callback_data = query.data
            
            if callback_data == "main_menu":
                keyboard = BotKeyboards.get_main_menu()
                await query.edit_message_text(
                    "🏠 主選單\n\n請選擇您要執行的操作：",
                    reply_markup=keyboard
                )
            
            elif callback_data == "status":
                await self.handlers.status_command(update, context)
            
            elif callback_data == "balance":
                await self.handlers.balance_command(update, context)
            
            elif callback_data == "mode":
                await self.handlers.mode_command(update, context)
            
            elif callback_data == "help":
                await self.handlers.help_command(update, context)
            
            else:
                await query.edit_message_text(f"未處理的回調: {callback_data}")
                
        except Exception as e:
            self.logger.error(f"處理回調查詢失敗: {e}")
            await query.edit_message_text(f"❌ 處理請求時發生錯誤: {str(e)}")
    
    async def _handle_text_message(self, update, context) -> None:
        """處理文本消息"""
        text = update.message.text
        
        # 快速命令處理
        quick_commands = {
            "📊 狀態": self.handlers.status_command,
            "💰 餘額": self.handlers.balance_command,
            "⚙️ 設置": self.handlers.mode_command,
            "❓ 幫助": self.handlers.help_command,
        }
        
        if text in quick_commands:
            await quick_commands[text](update, context)
        else:
            # 其他文本消息的處理
            await update.message.reply_text(
                f"❓ 不理解的命令: {text}\n\n請使用 /help 查看可用命令。"
            )
    
    async def _error_handler(self, update, context) -> None:
        """錯誤處理器"""
        self.logger.error(f"Telegram Bot 錯誤: {context.error}", exc_info=context.error)
        
        # 發送錯誤通知
        if self.notification_manager:
            await self.notification_manager.send_error_notification(
                error_type="Telegram Bot Error",
                error_message=str(context.error),
                context={
                    'update': str(update) if update else None,
                    'module': 'telegram_bot'
                }
            )
        
        # 向用戶發送友好的錯誤消息
        if update and update.effective_message:
            try:
                await update.effective_message.reply_text(
                    "❌ 系統暫時遇到問題，請稍後再試或聯繫管理員。"
                )
            except Exception as e:
                self.logger.error(f"發送錯誤消息失敗: {e}")
    
    async def start(self) -> None:
        """啟動 Telegram Bot"""
        if self.is_running:
            self.logger.warning("Bot 已在運行中")
            return

        max_retries = 3
        retry_delay = 5  # seconds

        for attempt in range(max_retries):
            try:
                self.logger.info(f"🚀 啟動 Telegram Bot... (第 {attempt + 1} 次嘗試)")

                # 初始化組件
                await self.initialize_components()

                # 設置信號處理器
                self._setup_signal_handlers()

                # 啟動應用
                await self.application.initialize()
                await self.application.start()

                # 開始輪詢
                await self.application.updater.start_polling(
                    allowed_updates=None,
                    drop_pending_updates=True
                )

                self.is_running = True
                self.logger.info("✅ Telegram Bot 啟動成功")

                # 發送啟動通知
                if self.notification_manager:
                    await self.notification_manager.send_system_status_update({
                        'is_healthy': True,
                        'current_mode': str(self.bot_controller.current_mode),
                        'uptime': '剛啟動',
                        'copy_system_status': True,
                        'strategy_system_status': True,
                        'database_status': True,
                        'network_status': True
                    })

                # 等待關閉信號
                await self._shutdown_event.wait()
                break  # 成功啟動後退出循環

            except TelegramError as e:
                self.logger.error(f"啟動 Bot 時發生 Telegram 錯誤: {e}")
                if attempt < max_retries - 1:
                    self.logger.info(f"將在 {retry_delay} 秒後重試...")
                    await asyncio.sleep(retry_delay)
                else:
                    self.logger.error("達到最大重試次數，啟動失敗。")
                    raise
            except Exception as e:
                self.logger.error(f"啟動 Bot 時發生未知錯誤: {e}")
                raise
            finally:
                if not self.is_running and attempt == max_retries - 1:
                     await self.stop()

        if not self.is_running:
             self.logger.info("Bot 未能成功啟動，程序即將退出。")
        else:
             await self.stop()
    
    async def stop(self) -> None:
        """停止 Telegram Bot"""
        if not self.is_running:
            return
        
        try:
            self.logger.info("🛑 正在停止 Telegram Bot...")
            
            # 停止會話管理器
            if self.session_manager:
                await self.session_manager.stop()
            
            # 停止交易活動
            if self.bot_controller:
                await self.bot_controller.stop_trading()
            
            # 停止 Telegram 應用
            if self.application:
                await self.application.updater.stop()
                await self.application.stop()
                await self.application.shutdown()
            
            self.is_running = False
            self.logger.info("✅ Telegram Bot 已停止")
            
        except Exception as e:
            self.logger.error(f"停止 Bot 時發生錯誤: {e}")
    
    def _setup_signal_handlers(self) -> None:
        """設置信號處理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"收到信號 {signum}，準備關閉...")
            self._shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def send_notification(self, notification_type: str, **kwargs) -> bool:
        """發送通知
        
        Args:
            notification_type: 通知類型
            **kwargs: 通知參數
        
        Returns:
            是否發送成功
        """
        if not self.notification_manager:
            return False
        
        try:
            if notification_type == "trade":
                await self.notification_manager.send_trade_notification(
                    kwargs.get('trade_type'),
                    kwargs.get('success'),
                    kwargs.get('details', {})
                )
            elif notification_type == "error":
                await self.notification_manager.send_error_notification(
                    kwargs.get('error_type'),
                    kwargs.get('error_message'),
                    kwargs.get('context', {})
                )
            elif notification_type == "balance_warning":
                await self.notification_manager.send_balance_warning(
                    kwargs.get('balance_info', {})
                )
            elif notification_type == "daily_summary":
                await self.notification_manager.send_daily_summary(
                    kwargs.get('summary_data', {})
                )
            else:
                self.logger.warning(f"未知的通知類型: {notification_type}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"發送通知失敗: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """獲取 Bot 狀態
        
        Returns:
            狀態字典
        """
        return {
            'telegram_bot_running': self.is_running,
            'application_initialized': self.application is not None,
            'bot_controller_initialized': self.bot_controller is not None,
            'notification_manager_initialized': self.notification_manager is not None,
            'handlers_initialized': self.handlers is not None
        }


async def main():
    """主函數 - 用於測試"""
    from src.core.config import Config
    
    # 初始化配置
    config = Config()
    
    # 創建並啟動 Bot
    bot = TelegramBot(config)
    
    try:
        await bot.start()
    except KeyboardInterrupt:
        print("\n收到中斷信號，正在關閉...")
    except Exception as e:
        print(f"Bot 運行時發生錯誤: {e}")
    finally:
        await bot.stop()


if __name__ == "__main__":
    # 設置事件循環策略
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())