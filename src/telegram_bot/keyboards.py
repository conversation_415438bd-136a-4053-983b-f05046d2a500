#!/usr/bin/env python3
"""
Telegram Bot 鍵盤介面模組
定義所有的內聯鍵盤和回復鍵盤
"""

from telegram import InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup, KeyboardButton
from typing import List, Optional


class BotKeyboards:
    """Telegram Bot 鍵盤介面管理類 - 優化版本"""
    
    @staticmethod
    def get_main_menu(is_running: bool = False, current_mode: str = "停用", has_balance: bool = False) -> InlineKeyboardMarkup:
        """獲取智能主選單鍵盤
        
        Args:
            is_running: 系統是否運行中
            current_mode: 當前交易模式
            has_balance: 是否有足夠餘額
        """
        # 第一行：核心控制（最重要）
        if is_running:
            control_buttons = [
                InlineKeyboardButton("🛑 緊急停止", callback_data="emergency_stop"),
                InlineKeyboardButton(f"🔄 切換模式 ({current_mode})", callback_data="mode")
            ]
        else:
            if has_balance:
                control_buttons = [
                    InlineKeyboardButton("🚀 快速啟動", callback_data="quick_start"),
                    InlineKeyboardButton("🎯 選擇模式", callback_data="mode")
                ]
            else:
                control_buttons = [
                    InlineKeyboardButton("⚠️ 需要充值", callback_data="balance"),
                    InlineKeyboardButton("🎯 選擇模式", callback_data="mode")
                ]
        
        keyboard = [
            control_buttons,  # 第一行：核心控制
            [  # 第二行：狀態監控
                InlineKeyboardButton("📊 實時狀態", callback_data="status"),
                InlineKeyboardButton("💰 我的錢包", callback_data="balance")
            ],
            [  # 第三行：核心功能
                InlineKeyboardButton("📈 交易數據", callback_data="trading_data"),
                InlineKeyboardButton("📋 歷史記錄", callback_data="history")
            ],
            [  # 第四行：輔助功能
                InlineKeyboardButton("⚙️ 高級設置", callback_data="settings"),
                InlineKeyboardButton("❓ 幫助支援", callback_data="help")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_quick_access_menu() -> InlineKeyboardMarkup:
        """獲取快速訪問選單（常用功能）"""
        keyboard = [
            [
                InlineKeyboardButton("⚡ 一鍵啟動V2跟單", callback_data="quick_copy"),
                InlineKeyboardButton("📈 一鍵啟動V1策略", callback_data="quick_strategy")
            ],
            [
                InlineKeyboardButton("🔍 快速檢查", callback_data="quick_check"),
                InlineKeyboardButton("🛑 全部停止", callback_data="stop_all")
            ],
            [
                InlineKeyboardButton("⬅️ 返回主選單", callback_data="main_menu")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_trading_mode_menu(current_mode: str = "停用", is_running: bool = False) -> InlineKeyboardMarkup:
        """獲取交易模式選擇鍵盤（狀態感知）
        
        Args:
            current_mode: 當前模式
            is_running: 是否運行中
        """
        # 動態按鈕文字（镸示當前狀態）
        copy_text = "✅ V2跟單" if current_mode == "V2跟單" and is_running else "🎯 V2跟單"
        strategy_text = "✅ V1策略" if current_mode == "V1策略" and is_running else "📈 V1策略"
        mixed_text = "✅ 混合模式" if current_mode == "混合" and is_running else "🔀 混合模式"
        disabled_text = "✅ 已停止" if not is_running else "🛑 停止所有"
        
        keyboard = [
            [
                InlineKeyboardButton(copy_text, callback_data="mode_copy"),
                InlineKeyboardButton(strategy_text, callback_data="mode_strategy")
            ],
            [
                InlineKeyboardButton(mixed_text, callback_data="mode_mixed"),
                InlineKeyboardButton(disabled_text, callback_data="mode_disabled")
            ]
        ]
        
        # 根據當前狀態添加快速操作
        if is_running:
            keyboard.append([
                InlineKeyboardButton("🛑 緊急停止", callback_data="emergency_stop"),
                InlineKeyboardButton("📈 查看狀態", callback_data="status")
            ])
        else:
            keyboard.append([
                InlineKeyboardButton("🚀 快速啟動", callback_data="quick_start_last"),
                InlineKeyboardButton("⚙️ 高級設置", callback_data="mode_settings")
            ])
        
        keyboard.append([
            InlineKeyboardButton("⬅️ 返回主選單", callback_data="main_menu")
        ])
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_smart_copy_trading_menu(is_active: bool = False, monitored_wallets: int = 0) -> InlineKeyboardMarkup:
        """獲取智能跟單交易設置鍵盤
        
        Args:
            is_active: 跟單是否活躍
            monitored_wallets: 監控錢包數量
        """
        # 動態狀態顯示
        status_icon = "🟢" if is_active else "🔴"
        wallet_text = f"👥 監控錢包 ({monitored_wallets})"
        
        keyboard = [
            [  # 狀態和控制
                InlineKeyboardButton(f"{status_icon} 跟單狀態", callback_data="copy_status"),
                InlineKeyboardButton("📈 實時數據", callback_data="copy_realtime")
            ],
            [  # 核心設置
                InlineKeyboardButton(wallet_text, callback_data="copy_wallets"),
                InlineKeyboardButton("💰 跟單金額", callback_data="copy_amount")
            ],
            [  # 高級設置
                InlineKeyboardButton("⚡ 延遲設置", callback_data="copy_delay"),
                InlineKeyboardButton("🎯 篩選條件", callback_data="copy_filters")
            ]
        ]
        
        # 根據狀態添加控制按鈕
        if is_active:
            keyboard.append([
                InlineKeyboardButton("🛑 停止跟單", callback_data="copy_stop"),
                InlineKeyboardButton("🔄 重啟跟單", callback_data="copy_restart")
            ])
        else:
            keyboard.append([
                InlineKeyboardButton("🚀 開始跟單", callback_data="copy_start"),
                InlineKeyboardButton("⚙️ 快速設置", callback_data="copy_quick_setup")
            ])
        
        keyboard.append([
            InlineKeyboardButton("⬅️ 返回模式選單", callback_data="mode_menu")
        ])
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_smart_strategy_trading_menu(is_active: bool = False, opportunities: int = 0) -> InlineKeyboardMarkup:
        """獲取智能策略交易設置鍵盤
        
        Args:
            is_active: 策略是否活躍
            opportunities: 當前機會數量
        """
        # 動態狀態顯示
        status_icon = "🟢" if is_active else "🔴"
        opportunity_text = f"🎯 當前機會 ({opportunities})"
        
        keyboard = [
            [  # 狀態和監控
                InlineKeyboardButton(f"{status_icon} 策略狀態", callback_data="strategy_status"),
                InlineKeyboardButton(opportunity_text, callback_data="strategy_opportunities")
            ],
            [  # 核心設置
                InlineKeyboardButton("🎯 策略參數", callback_data="strategy_params"),
                InlineKeyboardButton("📈 市場篩選", callback_data="strategy_filters")
            ],
            [  # 高級設置
                InlineKeyboardButton("💰 資金管理", callback_data="strategy_funds"),
                InlineKeyboardButton("⏰ 執行時間", callback_data="strategy_timing")
            ]
        ]
        
        # 根據狀態添加控制按鈕
        if is_active:
            keyboard.append([
                InlineKeyboardButton("🛑 停止策略", callback_data="strategy_stop"),
                InlineKeyboardButton("🔄 重新掃描", callback_data="strategy_rescan")
            ])
        else:
            keyboard.append([
                InlineKeyboardButton("🚀 開始策略", callback_data="strategy_start"),
                InlineKeyboardButton("⚙️ 快速設置", callback_data="strategy_quick_setup")
            ])
        
        keyboard.append([
            InlineKeyboardButton("⬅️ 返回模式選單", callback_data="mode_menu")
        ])
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_enhanced_settings_menu(user_level: str = "basic") -> InlineKeyboardMarkup:
        """獲取增強型系統設置鍵盤
        
        Args:
            user_level: 用戶等級 (basic/advanced/admin)
        """
        keyboard = []
        
        # 基礎設置（所有用戶）
        keyboard.extend([
            [
                InlineKeyboardButton("🔐 安全設置", callback_data="settings_security"),
                InlineKeyboardButton("💰 風險管理", callback_data="settings_risk")
            ],
            [
                InlineKeyboardButton("📢 通知設置", callback_data="settings_notifications"),
                InlineKeyboardButton("🌏 語言設置", callback_data="settings_language")
            ]
        ])
        
        # 高級設置（進階用戶）
        if user_level in ["advanced", "admin"]:
            keyboard.extend([
                [
                    InlineKeyboardButton("📈 性能調優", callback_data="settings_performance"),
                    InlineKeyboardButton("📊 日誌設置", callback_data="settings_logging")
                ],
                [
                    InlineKeyboardButton("🔌 API配置", callback_data="settings_api"),
                    InlineKeyboardButton("📤 數據備份", callback_data="settings_backup")
                ]
            ])
        
        # 管理員設置（僅管理員）
        if user_level == "admin":
            keyboard.extend([
                [
                    InlineKeyboardButton("👥 用戶管理", callback_data="settings_users"),
                    InlineKeyboardButton("🔄 系統重啟", callback_data="settings_restart")
                ],
                [
                    InlineKeyboardButton("📤 導出數據", callback_data="settings_export"),
                    InlineKeyboardButton("🔧 系統維護", callback_data="settings_maintenance")
                ]
            ])
        
        keyboard.append([
            InlineKeyboardButton("⬅️ 返回主選單", callback_data="main_menu")
        ])
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_contextual_help_keyboard(current_page: str = "main") -> InlineKeyboardMarkup:
        """獲取上下文相關的幫助鍵盤
        
        Args:
            current_page: 當前所在頁面
        """
        keyboard = []
        
        if current_page == "main":
            keyboard = [
                [
                    InlineKeyboardButton("🚀 快速入門", callback_data="help_quickstart"),
                    InlineKeyboardButton("🔍 常見問題", callback_data="help_faq")
                ],
                [
                    InlineKeyboardButton("🎯 交易模式說明", callback_data="help_trading_modes"),
                    InlineKeyboardButton("🔒 安全指南", callback_data="help_security")
                ]
            ]
        elif current_page == "trading":
            keyboard = [
                [
                    InlineKeyboardButton("🎯 V2跟單教學", callback_data="help_copy_trading"),
                    InlineKeyboardButton("📈 V1策略教學", callback_data="help_strategy_trading")
                ],
                [
                    InlineKeyboardButton("⚡ 延遲優化", callback_data="help_delay_optimization"),
                    InlineKeyboardButton("💰 資金管理", callback_data="help_risk_management")
                ]
            ]
        elif current_page == "settings":
            keyboard = [
                [
                    InlineKeyboardButton("🔐 安全設置", callback_data="help_security_settings"),
                    InlineKeyboardButton("📢 通知設置", callback_data="help_notifications")
                ],
                [
                    InlineKeyboardButton("🔧 故障排除", callback_data="help_troubleshooting"),
                    InlineKeyboardButton("📆 更新記錄", callback_data="help_changelog")
                ]
            ]
        
        # 常駐選項
        keyboard.extend([
            [
                InlineKeyboardButton("📞 聯繫支援", callback_data="help_contact"),
                InlineKeyboardButton("🔗 相關鏈結", callback_data="help_links")
            ],
            [
                InlineKeyboardButton("⬅️ 返回", callback_data="back")
            ]
        ])
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_emergency_control_keyboard() -> InlineKeyboardMarkup:
        """獲取緊急控制鍵盤（關鍵操作）"""
        keyboard = [
            [
                InlineKeyboardButton("🚑 緊急停止所有交易", callback_data="emergency_stop_all")
            ],
            [
                InlineKeyboardButton("🛡️ 啟動風險保護", callback_data="emergency_risk_protection"),
                InlineKeyboardButton("🔒 鎖定錢包", callback_data="emergency_lock_wallet")
            ],
            [
                InlineKeyboardButton("📞 聯繫管理員", callback_data="emergency_contact_admin"),
                InlineKeyboardButton("📄 導出日誌", callback_data="emergency_export_logs")
            ],
            [
                InlineKeyboardButton("⬅️ 取消", callback_data="cancel_emergency")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_confirmation_keyboard(action: str) -> InlineKeyboardMarkup:
        """獲取確認操作鍵盤

        Args:
            action: 需要確認的操作類型
        """
        keyboard = [
            [
                InlineKeyboardButton("✅ 確認", callback_data=f"confirm_{action}"),
                InlineKeyboardButton("❌ 取消", callback_data=f"cancel_{action}")
            ],
            [
                InlineKeyboardButton("⬅️ 返回模式選單", callback_data="mode_menu")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)

    @staticmethod
    def get_post_action_keyboard(action_type: str = "general") -> InlineKeyboardMarkup:
        """獲取操作完成後的導航鍵盤

        Args:
            action_type: 操作類型 (success, error, general)
        """
        if action_type == "success":
            keyboard = [
                [
                    InlineKeyboardButton("📊 查看狀態", callback_data="status"),
                    InlineKeyboardButton("🔄 切換模式", callback_data="mode_menu")
                ],
                [
                    InlineKeyboardButton("💰 檢查錢包", callback_data="balance"),
                    InlineKeyboardButton("🛑 緊急停止", callback_data="emergency_stop")
                ],
                [
                    InlineKeyboardButton("🏠 返回主選單", callback_data="main_menu")
                ]
            ]
        elif action_type == "error":
            keyboard = [
                [
                    InlineKeyboardButton("🔄 重新嘗試", callback_data="mode_menu"),
                    InlineKeyboardButton("💰 檢查錢包", callback_data="balance")
                ],
                [
                    InlineKeyboardButton("📞 聯繫支援", callback_data="help_contact"),
                    InlineKeyboardButton("📋 查看日誌", callback_data="help_troubleshooting")
                ],
                [
                    InlineKeyboardButton("🏠 返回主選單", callback_data="main_menu")
                ]
            ]
        else:  # general
            keyboard = [
                [
                    InlineKeyboardButton("📊 查看狀態", callback_data="status"),
                    InlineKeyboardButton("🔄 模式管理", callback_data="mode_menu")
                ],
                [
                    InlineKeyboardButton("🏠 返回主選單", callback_data="main_menu")
                ]
            ]

        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_amount_selection_keyboard() -> InlineKeyboardMarkup:
        """獲取百分比選擇鍵盤"""
        keyboard = [
            [
                InlineKeyboardButton("5%", callback_data="percent_5"),
                InlineKeyboardButton("10%", callback_data="percent_10"),
                InlineKeyboardButton("15%", callback_data="percent_15")
            ],
            [
                InlineKeyboardButton("20%", callback_data="percent_20"),
                InlineKeyboardButton("25%", callback_data="percent_25"),
                InlineKeyboardButton("50%", callback_data="percent_50")
            ],
            [
                InlineKeyboardButton("📊 自定義比例", callback_data="percent_custom"),
                InlineKeyboardButton("⬅️ 返回", callback_data="back")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_time_selection_keyboard() -> InlineKeyboardMarkup:
        """獲取時間選擇鍵盤"""
        keyboard = [
            [
                InlineKeyboardButton("1小時", callback_data="time_1h"),
                InlineKeyboardButton("2小時", callback_data="time_2h"),
                InlineKeyboardButton("4小時", callback_data="time_4h")
            ],
            [
                InlineKeyboardButton("6小時", callback_data="time_6h"),
                InlineKeyboardButton("12小時", callback_data="time_12h"),
                InlineKeyboardButton("24小時", callback_data="time_24h")
            ],
            [
                InlineKeyboardButton("⏰ 自定義時間", callback_data="time_custom"),
                InlineKeyboardButton("⬅️ 返回", callback_data="back")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_priority_selection_keyboard() -> InlineKeyboardMarkup:
        """獲取優先級選擇鍵盤"""
        keyboard = [
            [
                InlineKeyboardButton("🐌 低優先級", callback_data="priority_low"),
                InlineKeyboardButton("⚡ 標準", callback_data="priority_normal")
            ],
            [
                InlineKeyboardButton("🚀 高優先級", callback_data="priority_high"),
                InlineKeyboardButton("💥 緊急", callback_data="priority_urgent")
            ],
            [
                InlineKeyboardButton("⬅️ 返回", callback_data="back")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_wallet_management_keyboard() -> InlineKeyboardMarkup:
        """獲取錢包管理鍵盤"""
        keyboard = [
            [
                InlineKeyboardButton("➕ 添加錢包", callback_data="wallet_add"),
                InlineKeyboardButton("➖ 移除錢包", callback_data="wallet_remove")
            ],
            [
                InlineKeyboardButton("📋 錢包列表", callback_data="wallet_list"),
                InlineKeyboardButton("📊 錢包統計", callback_data="wallet_stats")
            ],
            [
                InlineKeyboardButton("🔄 刷新數據", callback_data="wallet_refresh"),
                InlineKeyboardButton("⬅️ 返回", callback_data="back")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_quick_actions_keyboard() -> ReplyKeyboardMarkup:
        """獲取快速操作鍵盤（回復鍵盤）"""
        keyboard = [
            [KeyboardButton("📊 狀態"), KeyboardButton("💰 餘額")],
            [KeyboardButton("🚀 開始"), KeyboardButton("🛑 停止")],
            [KeyboardButton("⚙️ 設置"), KeyboardButton("📋 歷史")]
        ]
        return ReplyKeyboardMarkup(
            keyboard,
            resize_keyboard=True,
            one_time_keyboard=False,
            input_field_placeholder="選擇操作..."
        )
    
    @staticmethod
    def get_navigation_keyboard(current_page: int, total_pages: int, prefix: str) -> InlineKeyboardMarkup:
        """獲取分頁導航鍵盤
        
        Args:
            current_page: 當前頁碼
            total_pages: 總頁數
            prefix: 回調數據前綴
        """
        keyboard = []
        
        nav_row = []
        
        # 上一頁按鈕
        if current_page > 1:
            nav_row.append(InlineKeyboardButton("⬅️ 上一頁", callback_data=f"{prefix}_page_{current_page - 1}"))
        
        # 頁碼信息
        nav_row.append(InlineKeyboardButton(f"{current_page}/{total_pages}", callback_data="page_info"))
        
        # 下一頁按鈕
        if current_page < total_pages:
            nav_row.append(InlineKeyboardButton("下一頁 ➡️", callback_data=f"{prefix}_page_{current_page + 1}"))
        
        keyboard.append(nav_row)
        
        # 返回按鈕
        keyboard.append([InlineKeyboardButton("⬅️ 返回", callback_data="back")])
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_toggle_keyboard(is_enabled: bool, feature: str) -> InlineKeyboardMarkup:
        """獲取開關切換鍵盤
        
        Args:
            is_enabled: 當前是否啟用
            feature: 功能名稱
        """
        button_text = "🔴 關閉" if is_enabled else "🟢 開啟"
        callback_data = f"toggle_{feature}_{'off' if is_enabled else 'on'}"
        
        keyboard = [
            [InlineKeyboardButton(button_text, callback_data=callback_data)],
            [InlineKeyboardButton("⬅️ 返回", callback_data="back")]
        ]
        return InlineKeyboardMarkup(keyboard)