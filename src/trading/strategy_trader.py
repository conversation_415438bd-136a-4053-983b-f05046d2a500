"""Strategy trader for executing automated trading decisions."""

import asyncio
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from decimal import Decimal
from datetime import datetime, timedelta

from src.core.config import Config
from src.core.exceptions import TradingError
from src.utils.logging_config import get_logger
from src.database.repository import Repository
from src.web3_tools.wallet import WalletManager
from src.trading.polymarket_client import PolymarketClient, TradeRequest, TradeResult
from src.scanning.market_scanner import MarketScanner, MarketOpportunity
from src.telegram_bot.notifications import NotificationManager


@dataclass
class StrategySettings:
    """Strategy trading configuration."""
    max_trade_amount: Decimal = Decimal('50')
    min_confidence_score: Decimal = Decimal('75')
    max_daily_trades: int = 10
    position_size_ratio: Decimal = Decimal('0.02')  # 2% of balance per trade
    stop_loss_enabled: bool = True
    max_positions: int = 5
    risk_per_trade: Decimal = Decimal('0.01')  # 1% risk per trade


@dataclass
class TradingDecision:
    """Trading decision data."""
    opportunity: MarketOpportunity
    action: str  # 'buy', 'sell', 'hold'
    amount: Decimal
    reason: str
    confidence: Decimal
    risk_assessment: str


class StrategyTrader:
    """Automated strategy trading engine."""
    
    def __init__(
        self,
        config: Config,
        repository: Repository,
        wallet_manager: WalletManager,
        polymarket_client: PolymarketClient,
        market_scanner: MarketScanner,
        settings: Optional[StrategySettings] = None,
        notification_manager: Optional[NotificationManager] = None
    ):
        """Initialize strategy trader."""
        self.config = config
        self.repository = repository
        self.wallet_manager = wallet_manager
        self.polymarket_client = polymarket_client
        self.market_scanner = market_scanner
        self.settings = settings or StrategySettings()
        self.notification_manager = notification_manager
        self.logger = get_logger(__name__)
        
        # Runtime state
        self.is_running = False
        self.active_user_id: Optional[int] = None
        self.daily_trades = 0
        self.active_positions = {}
        self.last_reset_date = datetime.utcnow().date()
        
        # Performance tracking
        self.stats = {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_pnl': Decimal('0'),
            'total_volume': Decimal('0'),
            'best_trade_return': Decimal('0'),
            'worst_trade_return': Decimal('0'),
            'avg_trade_return': Decimal('0')
        }
        
        self.logger.info("StrategyTrader initialized")
    
    async def start_trading(self, user_id: int) -> None:
        """Start automated strategy trading for a specific user."""
        if self.is_running:
            return
        
        self.is_running = True
        self.active_user_id = user_id
        self.logger.info(f"🚀 Starting strategy trading for user {user_id}...")
        
        try:
            while self.is_running:
                await self._trading_cycle()
                await asyncio.sleep(300)  # 5-minute cycle
                
        except Exception as e:
            self.logger.error(f"Strategy trading error for user {self.active_user_id}: {e}")
            if self.notification_manager and self.active_user_id:
                await self.notification_manager.send_trade_failure_notification(self.active_user_id, {'error': str(e), 'market_title': 'Trading Loop Error'})
        finally:
            self.is_running = False
            self.active_user_id = None
    
    async def stop_trading(self) -> None:
        """Stop automated trading."""
        self.is_running = False
        self.logger.info(f"🛑 Strategy trading stopped for user {self.active_user_id}")
        self.active_user_id = None
    
    async def _trading_cycle(self) -> None:
        """Execute one trading cycle."""
        try:
            # Reset daily counters if new day
            self._check_daily_reset()
            
            # Check daily limits
            if self.daily_trades >= self.settings.max_daily_trades:
                self.logger.info("Daily trade limit reached")
                return
            
            # Scan for opportunities
            async with self.market_scanner:
                opportunities = await self.market_scanner.scan_markets()
            
            # Analyze each opportunity
            for opportunity in opportunities:
                if not self.is_running:
                    break
                    
                decision = await self.analyze_opportunity(opportunity)
                
                if decision.action == 'buy':
                    await self._execute_strategy_trade(decision)
                    
                    # Respect trade limits
                    self.daily_trades += 1
                    if self.daily_trades >= self.settings.max_daily_trades:
                        break
            
            # Monitor existing positions
            await self._monitor_positions()
            
        except Exception as e:
            self.logger.error(f"Trading cycle error: {e}")
            if self.notification_manager and self.active_user_id:
                await self.notification_manager.send_trade_failure_notification(self.active_user_id, {'error': str(e), 'market_title': 'Trading Cycle'})

    async def analyze_opportunity(self, opportunity: MarketOpportunity) -> TradingDecision:
        """Analyze market opportunity and make trading decision."""
        try:
            # Skip if confidence too low
            if opportunity.confidence_score < self.settings.min_confidence_score:
                return TradingDecision(
                    opportunity=opportunity,
                    action='hold',
                    amount=Decimal('0'),
                    reason=f"Confidence {opportunity.confidence_score} below threshold {self.settings.min_confidence_score}",
                    confidence=opportunity.confidence_score,
                    risk_assessment='low'
                )
            
            # Check if already have position
            if opportunity.condition_id in self.active_positions:
                return TradingDecision(
                    opportunity=opportunity,
                    action='hold',
                    amount=Decimal('0'),
                    reason="Already have position in this market",
                    confidence=opportunity.confidence_score,
                    risk_assessment='low'
                )
            
            # Check position limits
            if len(self.active_positions) >= self.settings.max_positions:
                return TradingDecision(
                    opportunity=opportunity,
                    action='hold',
                    amount=Decimal('0'),
                    reason="Maximum positions reached",
                    confidence=opportunity.confidence_score,
                    risk_assessment='medium'
                )
            
            # Calculate position size
            balance = self.wallet_manager.get_balance()['USDC']
            position_size = min(
                balance * self.settings.position_size_ratio,
                self.settings.max_trade_amount
            )
            
            # Risk management
            risk_amount = balance * self.settings.risk_per_trade
            max_loss_price = opportunity.target_price
            if max_loss_price > 0:
                max_position_for_risk = risk_amount / max_loss_price
                position_size = min(position_size, max_position_for_risk)
            
            if position_size < Decimal('5'):  # Minimum trade size
                return TradingDecision(
                    opportunity=opportunity,
                    action='hold',
                    amount=Decimal('0'),
                    reason="Position size too small",
                    confidence=opportunity.confidence_score,
                    risk_assessment='low'
                )
            
            return TradingDecision(
                opportunity=opportunity,
                action='buy',
                amount=position_size,
                reason=f"High confidence opportunity: {opportunity.confidence_score}%",
                confidence=opportunity.confidence_score,
                risk_assessment=opportunity.risk_level
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing opportunity: {e}")
            return TradingDecision(
                opportunity=opportunity,
                action='hold',
                amount=Decimal('0'),
                reason=f"Analysis error: {str(e)}",
                confidence=Decimal('0'),
                risk_assessment='high'
            )
    
    async def _execute_strategy_trade(self, decision: TradingDecision) -> None:
        """Execute a strategy trade based on decision."""
        trade_details = {
            'amount': decision.amount,
            'market_title': decision.opportunity.question,
            'trade_type': 'strategy'
        }
        try:
            opportunity = decision.opportunity
            
            trade_request = TradeRequest(
                condition_id=opportunity.condition_id,
                outcome_index=opportunity.target_outcome,
                amount_usdc=decision.amount,
                side='buy',
                max_slippage=Decimal('0.02')
            )
            
            self.logger.info(
                "Executing strategy trade",
                condition_id=opportunity.condition_id,
                question=opportunity.question[:100],
                amount=decision.amount,
                confidence=decision.confidence,
                target_outcome=opportunity.target_outcome
            )
            
            # Execute trade
            result = await self.polymarket_client.execute_buy_order(trade_request)
            
            if result.success:
                # Track position
                self.active_positions[opportunity.condition_id] = {
                    'opportunity': opportunity,
                    'entry_price': result.actual_price,
                    'amount': decision.amount,
                    'shares': result.shares_received,
                    'entry_time': time.time(),
                    'tx_hash': result.tx_hash
                }
                
                # Update stats
                self.stats['total_trades'] += 1
                self.stats['total_volume'] += decision.amount
                
                self.logger.info(
                    "✅ Strategy trade executed successfully",
                    tx_hash=result.tx_hash,
                    shares_received=result.shares_received
                )
                
                # Store in database
                self._store_strategy_trade(decision, result)
                
            else:
                self.logger.error(f"❌ Strategy trade failed: {result.error}")
                self.stats['failed_trades'] += 1
                if self.notification_manager and self.active_user_id:
                    trade_details['error'] = result.error
                    await self.notification_manager.send_trade_failure_notification(self.active_user_id, trade_details)
                
        except Exception as e:
            self.logger.error(f"Error executing strategy trade: {e}", exc_info=True)
            self.stats['failed_trades'] += 1
            if self.notification_manager and self.active_user_id:
                trade_details['error'] = str(e)
                await self.notification_manager.send_trade_failure_notification(self.active_user_id, trade_details)

    async def _monitor_positions(self) -> None:
        """Monitor existing positions for exit conditions."""
        current_time = time.time()
        positions_to_close = []
        
        for condition_id, position in self.active_positions.items():
            try:
                opportunity = position['opportunity']
                
                # Check if market expired
                if opportunity.end_date <= datetime.utcnow():
                    positions_to_close.append(condition_id)
                    continue
                
                # Check time-based exit (e.g., close 30 minutes before expiry)
                time_to_expiry = (opportunity.end_date - datetime.utcnow()).total_seconds() / 3600
                if time_to_expiry <= 0.5:  # 30 minutes
                    positions_to_close.append(condition_id)
                    continue
                
                # Position age check (close after 4 hours regardless)
                position_age = (current_time - position['entry_time']) / 3600
                if position_age >= 4:
                    positions_to_close.append(condition_id)
                    continue
                
            except Exception as e:
                self.logger.error(f"Error monitoring position {condition_id}: {e}")
        
        # Close positions that meet exit criteria
        for condition_id in positions_to_close:
            await self._close_position(condition_id)
    
    async def _close_position(self, condition_id: str) -> None:
        """Close a position."""
        try:
            position = self.active_positions[condition_id]
            opportunity = position['opportunity']
            
            # In a real implementation, this would sell the shares
            # For now, we'll just calculate theoretical PnL and clean up
            
            self.logger.info(
                "Closing position",
                condition_id=condition_id,
                question=opportunity.question[:100]
            )
            
            # Remove from active positions
            del self.active_positions[condition_id]
            
        except Exception as e:
            self.logger.error(f"Error closing position {condition_id}: {e}")
    
    def _store_strategy_trade(self, decision: TradingDecision, result: TradeResult) -> None:
        """Store strategy trade in database."""
        try:
            self.repository.create_trade_history(
                wallet_address=self.wallet_manager.get_trader_address(),
                condition_id=decision.opportunity.condition_id,
                outcome_index=decision.opportunity.target_outcome,
                side='buy',
                amount_usdc=float(decision.amount),
                tx_hash=result.tx_hash,
                source='strategy_trader',
                metadata={
                    'confidence_score': float(decision.confidence),
                    'risk_assessment': decision.risk_assessment,
                    'reason': decision.reason,
                    'question': decision.opportunity.question,
                    'strategy': 'high_probability',
                    'entry_price': float(result.actual_price) if result.actual_price else None,
                    'shares_received': float(result.shares_received) if result.shares_received else None
                }
            )
        except Exception as e:
            self.logger.error(f"Error storing strategy trade: {e}")
    
    def _check_daily_reset(self) -> None:
        """Check if daily counters need reset."""
        current_date = datetime.utcnow().date()
        if current_date > self.last_reset_date:
            self.daily_trades = 0
            self.last_reset_date = current_date
            self.logger.info("Daily trading counters reset")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get strategy trading performance statistics."""
        total_trades = self.stats['total_trades']
        success_rate = (
            self.stats['successful_trades'] / total_trades
            if total_trades > 0 else 0
        )
        
        return {
            'is_running': self.is_running,
            'total_trades': total_trades,
            'successful_trades': self.stats['successful_trades'],
            'failed_trades': self.stats['failed_trades'],
            'success_rate': success_rate,
            'total_pnl': float(self.stats['total_pnl']),
            'total_volume': float(self.stats['total_volume']),
            'active_positions': len(self.active_positions),
            'daily_trades': self.daily_trades,
            'daily_limit': self.settings.max_daily_trades,
            'position_limit': self.settings.max_positions
        }