"""Market scanner for identifying trading opportunities."""

import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from decimal import Decimal, InvalidOperation
import decimal
from datetime import datetime, timedelta
import aiohttp
import json

from src.core.config import Config
from src.core.exceptions import NetworkError, APIError
from src.utils.logging_config import get_logger
from src.database.repository import Repository


@dataclass
class MarketOpportunity:
    """Market opportunity data structure."""
    condition_id: str
    question: str
    end_date: datetime
    time_to_expiry: float  # Hours until expiry
    outcomes: List[str]
    outcome_prices: List[Decimal]
    target_outcome: int  # Index of outcome to trade
    target_price: Decimal
    confidence_score: Decimal  # 0-100 confidence in this opportunity
    volume_24h: Decimal
    liquidity: Decimal
    market_category: str
    risk_level: str  # 'low', 'medium', 'high'
    expected_return: Decimal  # Expected return percentage
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class MarketFilters:
    """Filters for market scanning."""
    min_time_to_expiry: float = 0.5      # Minimum hours to expiry (30 minutes)
    max_time_to_expiry: float = 168.0    # Maximum hours to expiry (1 week)
    min_price_threshold: Decimal = Decimal('0.85')  # Min price for high-prob events
    max_price_threshold: Decimal = Decimal('0.98')  # Max price for high-prob events
    min_volume_24h: Decimal = Decimal('10')         # Min 24h volume (lowered)
    min_liquidity: Decimal = Decimal('10')          # Min liquidity (lowered)
    excluded_categories: List[str] = None            # Categories to exclude
    min_confidence_score: Decimal = Decimal('60')   # Min confidence score (lowered)
    
    def __post_init__(self):
        if self.excluded_categories is None:
            self.excluded_categories = []


class MarketScanner:
    """Scanner for identifying high-probability trading opportunities."""
    
    def __init__(self, config: Config, repository: Repository):
        """Initialize market scanner.
        
        Args:
            config: Bot configuration
            repository: Database repository
        """
        self.config = config
        self.repository = repository
        self.logger = get_logger(__name__)
        
        # GraphQL endpoint for Polymarket
        self.graphql_url = "https://gamma-api.polymarket.com/query"
        
        # HTTP session
        self.session = None
        
        # Scanning configuration
        self.filters = MarketFilters()
        self.scan_interval = 300  # 5 minutes between scans
        
        # Cache for market data
        self.market_cache = {}
        self.cache_duration = 300  # 5 minutes cache
        
        # 🚀 優化：快到期市場分層過濾策略
        self.short_term_markets = {}  # 儲存3天內到期的市場
        self.last_full_scan_time = 0  # 上次完整掃描時間
        self.full_scan_interval = 3600  # 每小時做一次完整掃描
        self.trading_window = 24  # 交易目標：1天內到期
        self.monitoring_window = 72  # 監控範圍：3天內到期
        
        # Smart filtering cache - 智能過濾緩存
        self.monitored_markets = {}  # 3天內到期的市場緩存
        self.long_term_markets = set()  # 長期市場ID集合（避免重複掃描）
        self.last_full_scan_time = 0  # 上次完整掃描時間
        self.full_scan_interval = 3600  # 每小時做一次完整掃描
        
        # Tracking
        self.scanned_markets = set()
        self.identified_opportunities = []
        self.last_scan_time = 0
        
        # Performance metrics
        self.stats = {
            'total_scans': 0,
            'markets_analyzed': 0,
            'opportunities_found': 0,
            'successful_predictions': 0,
            'failed_predictions': 0,
            'scan_duration_avg': 0.0,
            'cache_hits': 0,  # 緩存命中次數
            'long_term_filtered': 0,  # 過濾掉的長期市場數
            'smart_scan_efficiency': 0.0  # 智能掃描效率
        }
        
        self.logger.info(
            "MarketScanner initialized with smart filtering",
            graphql_url=self.graphql_url,
            scan_interval=self.scan_interval,
            full_scan_interval=self.full_scan_interval
        )

    def _update_filters_from_user_settings(self, user_settings: Dict[str, Any]) -> None:
        """根據用戶設定更新過濾器參數

        Args:
            user_settings: 用戶策略設定字典
        """
        # 更新勝率閾值
        min_win_rate = user_settings.get("min_win_rate", 85.0)
        self.filters.min_price_threshold = Decimal(str(min_win_rate / 100.0))

        # 更新到期時間範圍
        max_expiry_days = user_settings.get("max_expiry_days", 1)
        self.filters.max_time_to_expiry = float(max_expiry_days * 24)  # 轉換為小時

        self.logger.info(
            f"Updated filters from user settings: win_rate={min_win_rate}%, expiry_days={max_expiry_days}"
        )
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={'Content-Type': 'application/json'}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def scan_markets(self, user_strategy_settings: Dict[str, Any] = None) -> List[MarketOpportunity]:
        """Scan for trading opportunities with smart filtering and user-specific settings.

        Args:
            user_strategy_settings: 用戶自定義策略設定，包含 min_win_rate, max_expiry_days 等

        Returns:
            List of identified market opportunities
        """
        # 如果提供了用戶設定，更新過濾器
        if user_strategy_settings:
            self._update_filters_from_user_settings(user_strategy_settings)
        scan_start = time.time()
        self.stats['total_scans'] += 1
        current_time = time.time()
        
        try:
            self.logger.info("Starting smart market scan...")
            
            # 決定掃描策略
            is_full_scan = (current_time - self.last_full_scan_time) > self.full_scan_interval
            
            if is_full_scan:
                print(f"\n🔄 執行完整掃描... 正在獲取最新市場數據")
                markets = await self._fetch_active_markets()
                await self._categorize_markets(markets)
                self.last_full_scan_time = current_time
            else:
                print(f"\n⚡ 執行智能掃描... 只關注3天內到期市場")
                markets = await self._get_monitored_markets()
            
            print(f"📈 本次掃描 {len(markets)} 個市場，開始逐一分析...")
            self.stats['markets_analyzed'] += len(markets)
            
            # Filter and analyze markets
            opportunities = []
            analyzed_count = 0
            for market in markets:
                analyzed_count += 1
                try:
                    print(f"\n--- 市場 {analyzed_count}/{len(markets)} ---")
                    opportunity = await self._analyze_market(market)
                    if opportunity:
                        opportunities.append(opportunity)
                        
                except Exception as e:
                    self.logger.error(f"Error analyzing market {market.get('id', 'unknown')}: {e}")
                    continue
            
            # Sort by confidence score
            opportunities.sort(key=lambda x: x.confidence_score, reverse=True)
            
            self.stats['opportunities_found'] += len(opportunities)
            self.identified_opportunities = opportunities
            self.last_scan_time = time.time()
            
            # Update average scan duration
            scan_duration = time.time() - scan_start
            total_scans = self.stats['total_scans']
            current_avg = self.stats['scan_duration_avg']
            self.stats['scan_duration_avg'] = (
                (current_avg * (total_scans - 1) + scan_duration) / total_scans
            )
            
            # Calculate smart scan efficiency
            if is_full_scan:
                self.stats['smart_scan_efficiency'] = 0  # 完整掃描
            else:
                total_markets = len(self.monitored_markets) + len(self.long_term_markets)
                if total_markets > 0:
                    self.stats['smart_scan_efficiency'] = (len(self.long_term_markets) / total_markets) * 100
            
            scan_type = "完整掃描" if is_full_scan else "智能掃描"
            self.logger.info(
                f"{scan_type} completed",
                markets_analyzed=len(markets),
                opportunities_found=len(opportunities),
                scan_duration=scan_duration,
                top_confidence=opportunities[0].confidence_score if opportunities else 0,
                cache_efficiency=f"{self.stats['smart_scan_efficiency']:.1f}%"
            )
            
            # Store opportunities in database
            await self._store_opportunities(opportunities)
            
            return opportunities
            
        except Exception as e:
            self.logger.error(f"Market scan failed: {e}")
            return []
    
    async def _fetch_active_markets(self) -> List[Dict[str, Any]]:
        """Fetch active markets from Polymarket REST API.
        
        Returns:
            List of market data
        """
        if not self.session:
            raise APIError("Session not initialized")
        
        try:
            # 使用公開的 REST API 代替需要認證的 GraphQL
            # 這是 Polymarket 的公開市場數據端點
            rest_url = "https://gamma-api.polymarket.com/markets"
            
            # 計算時間範圍
            now = datetime.utcnow()
            min_end_time = now + timedelta(hours=self.filters.min_time_to_expiry)
            max_end_time = now + timedelta(hours=self.filters.max_time_to_expiry)
            
            params = {
                "limit": 1000,  # 增加到1000以獲取更多市場
                "active": "true",
                "closed": "false",
                "order": "volume24hr",
                "direction": "desc"
                # 暫時移除 outcomeCount 參數，因為API返回的數據結構有問題
                # "outcomeCount": "2"  # 這個參數可能不起作用
            }
            
            async with self.session.get(rest_url, params=params) as response:
                if response.status != 200:
                    # 如果 REST API 也失敗，返回模擬數據以確保測試可以繼續
                    self.logger.warning(f"REST API failed with status {response.status}, using mock data")
                    return self._generate_mock_markets()
                
                data = await response.json()
                markets = data if isinstance(data, list) else data.get('data', [])
                
                # 記錄第一個市場的結構以供調試
                if markets and len(markets) > 0:
                    first_market = markets[0]
                    print(f"\n📁 API數據格式示例:")
                    print(f"   市場名稱: {first_market.get('question', 'N/A')[:50]}...")
                    print(f"   價格數據: {first_market.get('outcomePrices', 'N/A')}")
                    print(f"   選項數據: {first_market.get('outcomes', 'N/A')}")
                    print(f"   結束時間: {first_market.get('endDate', 'N/A')}")
                
                self.logger.info(
                    "Fetched markets from REST API",
                    total_markets=len(markets),
                    note="將在分析階段過濾"
                )
                
                return markets
                
        except Exception as e:
            self.logger.warning(f"Failed to fetch from REST API: {e}, using mock data")
            return self._generate_mock_markets()
    
    async def _categorize_markets(self, markets: List[Dict[str, Any]]) -> None:
        """將市場按到期時間分類並緩存。
        
        Args:
            markets: 原始市場數據列表
        """
        now = datetime.utcnow()
        new_monitored = {}
        new_long_term = set()
        
        categorized_count = {
            '1天內': 0,
            '3天內': 0, 
            '長期': 0,
            '已過期': 0
        }
        
        for market in markets:
            try:
                market_id = market.get('id')
                if not market_id:
                    continue
                    
                end_date = datetime.fromisoformat(market['endDate'].replace('Z', '+00:00'))
                time_to_expiry = (end_date - now.replace(tzinfo=end_date.tzinfo)).total_seconds() / 3600
                
                if time_to_expiry <= 0:
                    categorized_count['已過期'] += 1
                    continue
                elif time_to_expiry <= 24:  # 1天內
                    new_monitored[market_id] = market
                    categorized_count['1天內'] += 1
                elif time_to_expiry <= 72:  # 3天內
                    new_monitored[market_id] = market
                    categorized_count['3天內'] += 1
                else:  # 長期市場
                    new_long_term.add(market_id)
                    categorized_count['長期'] += 1
                    
            except Exception as e:
                self.logger.warning(f"Failed to categorize market {market.get('id', 'unknown')}: {e}")
                continue
        
        # 更新緩存
        self.monitored_markets = new_monitored
        self.long_term_markets = new_long_term
        self.stats['long_term_filtered'] = len(new_long_term)
        
        print(f"📋 市場分類完成:")
        print(f"   1天內到期: {categorized_count['1天內']}個 (重點交易目標)")
        print(f"   3天內到期: {categorized_count['3天內']}個 (持續監控)")
        print(f"   長期市場: {categorized_count['長期']}個 (已過濾，節省資源)")
        print(f"   已過期: {categorized_count['已過期']}個 (已忽略)")
        
        self.logger.info(
            "Markets categorized",
            monitored=len(new_monitored),
            long_term_filtered=len(new_long_term),
            total_processed=len(markets)
        )
    
    async def _get_monitored_markets(self) -> List[Dict[str, Any]]:
        """獲取監控中的短期市場。
        
        Returns:
            3天內到期的市場列表
        """
        now = datetime.utcnow()
        active_markets = []
        expired_count = 0
        
        # 檢查監控市場是否還有效
        for market_id, market in list(self.monitored_markets.items()):
            try:
                end_date = datetime.fromisoformat(market['endDate'].replace('Z', '+00:00'))
                time_to_expiry = (end_date - now.replace(tzinfo=end_date.tzinfo)).total_seconds() / 3600
                
                if time_to_expiry <= 0:
                    # 市場已過期，從監控列表中移除
                    del self.monitored_markets[market_id]
                    expired_count += 1
                else:
                    active_markets.append(market)
                    
            except Exception as e:
                self.logger.warning(f"Failed to check market {market_id}: {e}")
                # 出錯時移除該市場
                del self.monitored_markets[market_id]
                continue
        
        if expired_count > 0:
            print(f"🗑️ 清理已過期市場: {expired_count}個")
        
        print(f"⚡ 智能掃描: 關注{len(active_markets)}個短期市場 (節省{len(self.long_term_markets)}個長期市場的掃描時間)")
        
        self.stats['cache_hits'] += len(active_markets)
        
        return active_markets
    
    def _validate_market_data(self, market: Dict[str, Any]) -> bool:
        """Validate that market has required fields."""
        required_fields = ['conditionId', 'question', 'endDate', 'outcomes', 'outcomePrices']
        return all(field in market for field in required_fields)
    
    def _generate_mock_markets(self) -> List[Dict[str, Any]]:
        """Generate mock market data for testing when API is unavailable."""
        import random
        
        mock_markets = []
        questions = [
            "Will Bitcoin reach $100,000 by the end of 2024?",
            "Will the next US Presidential election be decided in November?", 
            "Will Apple's stock price exceed $200 this quarter?",
            "Will there be snow in New York City this winter?",
            "Will Ethereum 2.0 launch be completed this year?"
        ]
        
        categories = ["Crypto", "Politics", "Technology", "Weather", "Finance"]
        
        for i, question in enumerate(questions):
            # 生成隨機價格
            yes_price = random.uniform(0.75, 0.95)
            no_price = 1 - yes_price
            
            # 生成未來的結束時間
            hours_to_end = random.uniform(self.filters.min_time_to_expiry, self.filters.max_time_to_expiry)
            end_date = datetime.utcnow() + timedelta(hours=hours_to_end)
            
            mock_market = {
                'id': f'mock_{i}',
                'conditionId': f'0x{"0" * 60}{i:04d}',
                'question': question,
                'description': f'Mock market for testing: {question}',
                'endDate': end_date.isoformat() + 'Z',
                'volume24hr': str(random.randint(1000, 50000)),
                'liquidity': str(random.randint(500, 10000)),
                'outcomes': ['Yes', 'No'],
                'outcomePrices': [str(yes_price), str(no_price)],
                'category': categories[i % len(categories)],
                'active': True,
                'closed': False
            }
            mock_markets.append(mock_market)
        
        self.logger.info(f"Generated {len(mock_markets)} mock markets for testing")
        return mock_markets
    
    async def _analyze_market(self, market_data: Dict[str, Any]) -> Optional[MarketOpportunity]:
        """Analyze a market for trading opportunities.
        
        Args:
            market_data: Market data from GraphQL
            
        Returns:
            Market opportunity if found, None otherwise
        """
        try:
            # Parse basic market info
            condition_id = market_data['conditionId']
            question = market_data['question']
            end_date = datetime.fromisoformat(market_data['endDate'].replace('Z', '+00:00'))
            
            # 顯示正在分析的市場
            print(f"\n🔍 正在分析: {question[:80]}...")
            
            # Calculate time to expiry
            time_to_expiry = (end_date - datetime.utcnow().replace(tzinfo=end_date.tzinfo)).total_seconds() / 3600
            print(f"   到期時間: {end_date.strftime('%Y-%m-%d %H:%M')} (剩餘{time_to_expiry:.1f}小時)")
            
            # Parse prices and outcomes with error handling
            try:
                # 处理 outcomes 字段 (可能是 JSON 字串)
                outcomes_raw = market_data.get('outcomes', [])
                if isinstance(outcomes_raw, str):
                    try:
                        outcomes = json.loads(outcomes_raw)
                        print(f"   outcomes JSON解析: {outcomes}")
                    except json.JSONDecodeError:
                        print(f"   ⚠️ outcomes JSON解析失敗: {outcomes_raw}")
                        return None
                else:
                    outcomes = outcomes_raw
                
                # 处理 outcomePrices 字段 (可能是 JSON 字串)
                outcome_prices_raw = market_data.get('outcomePrices', [])
                outcome_prices = []
                
                print(f"   原始價格數據: {outcome_prices_raw} (類型: {type(outcome_prices_raw)})")
                
                if outcome_prices_raw is None or outcome_prices_raw == []:
                    print(f"   ⚠️ 沒有價格數據，跳過此市場")
                    return None
                    
                if isinstance(outcome_prices_raw, str):
                    try:
                        outcome_prices_list = json.loads(outcome_prices_raw)
                        print(f"   價格 JSON解析後: {outcome_prices_list}")
                    except json.JSONDecodeError:
                        print(f"   ⚠️ 價格 JSON解析失敗: {outcome_prices_raw}")
                        return None
                else:
                    outcome_prices_list = outcome_prices_raw
                
                # 解析每個價格元素
                for i, price in enumerate(outcome_prices_list):
                    try:
                        if isinstance(price, str):
                            clean_price = ''.join(c for c in price if c.isdigit() or c == '.')
                            if clean_price and '.' in clean_price:
                                outcome_prices.append(Decimal(clean_price))
                            elif clean_price:
                                outcome_prices.append(Decimal(clean_price))
                            else:
                                print(f"   ⚠️ 價格{i+1}無法解析: '{price}'，跳過此市場")
                                return None
                        elif isinstance(price, (int, float)):
                            outcome_prices.append(Decimal(str(price)))
                        else:
                            print(f"   ⚠️ 價格{i+1}類型未知: {price} (類型: {type(price)})，跳過此市場")
                            return None
                    except (ValueError, decimal.InvalidOperation) as e:
                        print(f"   ⚠️ 價格{i+1}解析錯誤: {price} -> {e}，跳過此市場")
                        return None
                        
                print(f"   解析結果: {len(outcome_prices)}個價格, {len(outcomes)}個選項")
                
            except Exception as e:
                print(f"   ❌ 價格數據解析錯誤: {e}")
                import traceback
                traceback.print_exc()
                return None
            
            if len(outcome_prices) < 1 or len(outcomes) < 1:
                print(f"   ❌ 跳過: 沒有有效的選項或價格數據")
                return None
            
            # 顯示所有選項和價格
            for i, (outcome, price) in enumerate(zip(outcomes, outcome_prices)):
                print(f"   選項{i+1}: {outcome} - {float(price)*100:.1f}%")
            
            # 找出概率最高的選項
            max_price_index = outcome_prices.index(max(outcome_prices))
            max_price = outcome_prices[max_price_index]
            max_outcome = outcomes[max_price_index]
            
            print(f"   🥇 最高概率選項: {max_outcome} - {float(max_price)*100:.1f}%")
            
            # Parse volume and liquidity
            volume_24h = Decimal(str(market_data.get('volume24hr', 0)))
            liquidity = Decimal(str(market_data.get('liquidity', 0)))
            category = market_data.get('category', 'Unknown')
            
            print(f"   24h交易量: ${float(volume_24h):.0f} | 流動性: ${float(liquidity):.0f} | 類別: {category}")
            
            # Apply basic filters
            print(f"   過濾檢查:")
            
            # Time filter
            if not (self.filters.min_time_to_expiry <= time_to_expiry <= self.filters.max_time_to_expiry):
                print(f"   ❌ 時間過濾: {time_to_expiry:.1f}h 不在 {self.filters.min_time_to_expiry}-{self.filters.max_time_to_expiry}h 範圍")
                return None
            print(f"   ✅ 時間符合: {time_to_expiry:.1f}h")
            
            # Volume filter
            if volume_24h < self.filters.min_volume_24h:
                print(f"   ❌ 交易量過濾: ${float(volume_24h):.0f} < ${float(self.filters.min_volume_24h):.0f}")
                return None
            print(f"   ✅ 交易量符合: ${float(volume_24h):.0f}")
            
            # Liquidity filter
            if liquidity < self.filters.min_liquidity:
                print(f"   ❌ 流動性過濾: ${float(liquidity):.0f} < ${float(self.filters.min_liquidity):.0f}")
                return None
            print(f"   ✅ 流動性符合: ${float(liquidity):.0f}")
            
            # Category filter
            if category in self.filters.excluded_categories:
                print(f"   ❌ 類別過濾: {category} 在排除清單中")
                return None
            print(f"   ✅ 類別符合: {category}")
            
            # 檢查是否符合高概率策略（最高選項 > 85%）
            min_high_prob_threshold = Decimal('0.85')
            
            if max_price > min_high_prob_threshold:
                print(f"   ✅ 符合策略: 最高選項 {float(max_price)*100:.1f}% > 85%")
                
                # Find trading opportunity
                opportunity = self._identify_opportunity(
                    condition_id=condition_id,
                    question=question,
                    end_date=end_date,
                    time_to_expiry=time_to_expiry,
                    outcomes=outcomes,
                    outcome_prices=outcome_prices,
                    volume_24h=volume_24h,
                    liquidity=liquidity,
                    category=category,
                    market_data=market_data,
                    target_outcome_index=max_price_index
                )
                
                if opportunity:
                    print(f"   🎉 發現機會! 信心分數: {float(opportunity.confidence_score):.1f}% | 風險: {opportunity.risk_level} | 預期回報: {float(opportunity.expected_return):.1f}%")
                
                return opportunity
            else:
                print(f"   ❌ 不符合: 最高選項 {float(max_price)*100:.1f}% ≤ 85%")
                return None
            
        except Exception as e:
            print(f"   ❌ 分析錯誤: {e}")
            self.logger.error(f"Error analyzing market: {e}")
            return None
    
    def _passes_basic_filters(
        self,
        time_to_expiry: float,
        volume_24h: Decimal,
        liquidity: Decimal,
        category: str
    ) -> bool:
        """Check if market passes basic filters.
        
        Args:
            time_to_expiry: Hours to expiry
            volume_24h: 24h volume
            liquidity: Current liquidity
            category: Market category
            
        Returns:
            True if passes all filters
        """
        # Time filter
        if not (self.filters.min_time_to_expiry <= time_to_expiry <= self.filters.max_time_to_expiry):
            return False
        
        # Volume filter
        if volume_24h < self.filters.min_volume_24h:
            return False
        
        # Liquidity filter
        if liquidity < self.filters.min_liquidity:
            return False
        
        # Category filter
        if category in self.filters.excluded_categories:
            return False
        
        return True
    
    def _identify_opportunity(
        self,
        condition_id: str,
        question: str,
        end_date: datetime,
        time_to_expiry: float,
        outcomes: List[str],
        outcome_prices: List[Decimal],
        volume_24h: Decimal,
        liquidity: Decimal,
        category: str,
        market_data: Dict[str, Any],
        target_outcome_index: int
    ) -> Optional[MarketOpportunity]:
        """Identify trading opportunity in market.
        
        適用於任何類型的市場，只要最高概率選項超過85%。
        
        Returns:
            Market opportunity if found
        """
        
        # 獲取目標選項的價格和名稱
        target_price = outcome_prices[target_outcome_index]
        target_outcome = outcomes[target_outcome_index]
        
        # 計算信心分數
        confidence = self._calculate_confidence_score(
            price=target_price,
            time_to_expiry=time_to_expiry,
            volume_24h=volume_24h,
            liquidity=liquidity,
            category=category,
            strategy='high_prob_any'
        )
        
        # 檢查是否達到最低信心闾值
        if confidence < self.filters.min_confidence_score:
            return None
        
        # 計算預期回報和風險等級
        expected_return = (Decimal('1') - target_price) / target_price * 100
        risk_level = self._assess_risk_level(target_price, time_to_expiry)
        
        return MarketOpportunity(
            condition_id=condition_id,
            question=question,
            end_date=end_date,
            time_to_expiry=time_to_expiry,
            outcomes=outcomes,
            outcome_prices=outcome_prices,
            target_outcome=target_outcome_index,
            target_price=target_price,
            confidence_score=confidence,
            volume_24h=volume_24h,
            liquidity=liquidity,
            market_category=category,
            risk_level=risk_level,
            expected_return=expected_return,
            metadata={
                'strategy': 'high_prob_any',
                'target_outcome_name': target_outcome,
                'market_data': market_data,
                'analysis_time': time.time()
            }
        )
    
    def _calculate_confidence_score(
        self,
        price: Decimal,
        time_to_expiry: float,
        volume_24h: Decimal,
        liquidity: Decimal,
        category: str,
        strategy: str
    ) -> Decimal:
        """Calculate confidence score for an opportunity.
        
        Args:
            price: Outcome price
            time_to_expiry: Hours to expiry
            volume_24h: 24h volume
            liquidity: Current liquidity
            category: Market category
            strategy: Trading strategy
            
        Returns:
            Confidence score (0-100)
        """
        score = Decimal('50')  # Base score
        
        # Price factor - higher price = higher confidence for high-prob strategies
        if strategy in ['high_prob_yes', 'high_prob_no', 'high_prob_any']:
            if price >= Decimal('0.90'):
                score += Decimal('20')
            elif price >= Decimal('0.85'):
                score += Decimal('15')
            elif price >= Decimal('0.80'):
                score += Decimal('10')
        
        # Time factor - less time = potentially higher confidence
        if time_to_expiry <= 2:
            score += Decimal('15')
        elif time_to_expiry <= 4:
            score += Decimal('10')
        elif time_to_expiry <= 6:
            score += Decimal('5')
        
        # Volume factor - higher volume = higher confidence
        if volume_24h >= Decimal('10000'):
            score += Decimal('15')
        elif volume_24h >= Decimal('5000'):
            score += Decimal('10')
        elif volume_24h >= Decimal('2000'):
            score += Decimal('5')
        
        # Liquidity factor
        if liquidity >= Decimal('2000'):
            score += Decimal('10')
        elif liquidity >= Decimal('1000'):
            score += Decimal('5')
        
        # Category factor - some categories might be more predictable
        category_multiplier = {
            'Politics': Decimal('1.0'),
            'Sports': Decimal('1.1'),
            'Crypto': Decimal('0.9'),
            'Pop Culture': Decimal('0.8'),
            'Business': Decimal('1.0')
        }.get(category, Decimal('1.0'))
        
        score *= category_multiplier
        
        # Ensure score is within bounds
        return max(Decimal('0'), min(score, Decimal('100')))
    
    def _assess_risk_level(self, price: Decimal, time_to_expiry: float) -> str:
        """Assess risk level for an opportunity.
        
        Args:
            price: Outcome price
            time_to_expiry: Hours to expiry
            
        Returns:
            Risk level string
        """
        # Higher price + less time = lower risk (for high-prob strategies)
        if price >= Decimal('0.90') and time_to_expiry <= 3:
            return 'low'
        elif price >= Decimal('0.85') and time_to_expiry <= 6:
            return 'medium'
        else:
            return 'high'
    
    async def _store_opportunities(self, opportunities: List[MarketOpportunity]) -> None:
        """Store identified opportunities in database.
        
        Args:
            opportunities: List of opportunities to store
        """
        try:
            for opportunity in opportunities:
                # Store market data for tracking
                self.repository.create_or_update_market_data(
                    condition_id=opportunity.condition_id,
                    question=opportunity.question,
                    end_date=opportunity.end_date.isoformat(),
                    outcomes=opportunity.outcomes,
                    current_prices=[float(p) for p in opportunity.outcome_prices],
                    volume_24h=float(opportunity.volume_24h),
                    liquidity=float(opportunity.liquidity),
                    category=opportunity.market_category,
                    metadata={
                        'confidence_score': float(opportunity.confidence_score),
                        'target_outcome': opportunity.target_outcome,
                        'target_price': float(opportunity.target_price),
                        'expected_return': float(opportunity.expected_return),
                        'risk_level': opportunity.risk_level,
                        'time_to_expiry': opportunity.time_to_expiry,
                        'strategy': opportunity.metadata.get('strategy'),
                        'scanner_version': '1.0'
                    }
                )
            
        except Exception as e:
            self.logger.error(f"Error storing opportunities: {e}")
    
    def get_current_opportunities(self) -> List[MarketOpportunity]:
        """Get current identified opportunities.
        
        Returns:
            List of current opportunities
        """
        # Filter out expired opportunities
        current_time = datetime.utcnow()
        valid_opportunities = [
            opp for opp in self.identified_opportunities
            if opp.end_date > current_time
        ]
        
        return valid_opportunities
    
    def update_filters(self, new_filters: MarketFilters) -> None:
        """Update scanning filters.
        
        Args:
            new_filters: New filter configuration
        """
        self.filters = new_filters
        self.logger.info(
            "Market scanning filters updated",
            min_time_to_expiry=self.filters.min_time_to_expiry,
            max_time_to_expiry=self.filters.max_time_to_expiry,
            min_price_threshold=self.filters.min_price_threshold,
            max_price_threshold=self.filters.max_price_threshold
        )
    
    def get_scanning_stats(self) -> Dict[str, Any]:
        """Get scanning performance statistics.
        
        Returns:
            Statistics dictionary
        """
        return {
            'total_scans': self.stats['total_scans'],
            'markets_analyzed': self.stats['markets_analyzed'],
            'opportunities_found': self.stats['opportunities_found'],
            'successful_predictions': self.stats['successful_predictions'],
            'failed_predictions': self.stats['failed_predictions'],
            'scan_duration_avg': self.stats['scan_duration_avg'],
            'last_scan_time': self.last_scan_time,
            'current_opportunities': len(self.get_current_opportunities()),
            'filters': {
                'min_time_to_expiry': float(self.filters.min_time_to_expiry),
                'max_time_to_expiry': float(self.filters.max_time_to_expiry),
                'min_price_threshold': float(self.filters.min_price_threshold),
                'max_price_threshold': float(self.filters.max_price_threshold),
                'min_volume_24h': float(self.filters.min_volume_24h),
                'min_confidence_score': float(self.filters.min_confidence_score)
            }
        }