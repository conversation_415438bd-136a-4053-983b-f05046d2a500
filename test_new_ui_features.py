#!/usr/bin/env python3
"""
測試新UI功能的完整測試腳本
按照UI_spec.md的要求測試所有新功能
"""

import sys
import asyncio
from unittest.mock import Mock, AsyncMock
sys.path.append('.')

from src.telegram_bot.keyboards import BotKeyboards
from src.telegram_bot.session_manager import SessionManager
from src.core.config import Config

def test_keyboard_layouts():
    """測試所有新的鍵盤布局"""
    print("=== 測試鍵盤布局 ===")
    
    # 測試主選單 - 2x3布局
    print("1. 測試主選單鍵盤...")
    main_menu = BotKeyboards.get_main_menu()
    assert len(main_menu.inline_keyboard) == 3, "主選單應該有3行"
    assert len(main_menu.inline_keyboard[0]) == 2, "第一行應該有2個按鈕"
    assert len(main_menu.inline_keyboard[1]) == 2, "第二行應該有2個按鈕"
    assert len(main_menu.inline_keyboard[2]) == 2, "第三行應該有2個按鈕"
    print("✅ 主選單鍵盤測試通過")
    
    # 測試錢包狀態鍵盤
    print("2. 測試錢包狀態鍵盤...")
    wallet_menu = BotKeyboards.get_wallet_status_menu()
    assert len(wallet_menu.inline_keyboard) >= 2, "錢包狀態鍵盤應該至少有2行"
    print("✅ 錢包狀態鍵盤測試通過")
    
    # 測試策略機器人鍵盤
    print("3. 測試策略機器人鍵盤...")
    strategy_menu = BotKeyboards.get_strategy_bot_menu()
    assert len(strategy_menu.inline_keyboard) >= 2, "策略機器人鍵盤應該至少有2行"
    print("✅ 策略機器人鍵盤測試通過")
    
    # 測試策略設定鍵盤
    print("4. 測試策略設定鍵盤...")
    settings_menu = BotKeyboards.get_strategy_settings_menu()
    assert len(settings_menu.inline_keyboard) >= 3, "策略設定鍵盤應該至少有3行"
    print("✅ 策略設定鍵盤測試通過")
    
    # 測試跟單機器人鍵盤
    print("5. 測試跟單機器人鍵盤...")
    copy_menu = BotKeyboards.get_copy_bot_menu()
    assert len(copy_menu.inline_keyboard) >= 2, "跟單機器人鍵盤應該至少有2行"
    print("✅ 跟單機器人鍵盤測試通過")
    
    # 測試跟單設定鍵盤
    print("6. 測試跟單設定鍵盤...")
    copy_settings = BotKeyboards.get_copy_trader_settings_menu()
    assert len(copy_settings.inline_keyboard) >= 4, "跟單設定鍵盤應該至少有4行"
    print("✅ 跟單設定鍵盤測試通過")
    
    print("✅ 所有鍵盤布局測試通過！\n")

def test_user_strategy_settings():
    """測試用戶策略設定管理"""
    print("=== 測試用戶策略設定管理 ===")
    
    config = Config()
    session_manager = SessionManager(config)
    
    # 測試用戶ID
    test_user_id = 12345
    
    # 創建用戶會話
    print("1. 創建用戶會話...")
    session = session_manager.get_or_create_session(test_user_id, 'test_user', 12345)
    assert session is not None, "用戶會話創建失敗"
    print("✅ 用戶會話創建成功")
    
    # 測試預設策略設定
    print("2. 測試預設策略設定...")
    default_settings = session_manager.get_user_strategy_settings(test_user_id)
    expected_defaults = {
        'min_win_rate': 85.0,
        'max_expiry_days': 1,
        'trade_amount': 10.0
    }
    assert default_settings == expected_defaults, f"預設設定不正確: {default_settings}"
    print("✅ 預設策略設定正確")
    
    # 測試更新策略設定
    print("3. 測試更新策略設定...")
    session_manager.update_user_strategy_setting(test_user_id, 'min_win_rate', 90.0)
    session_manager.update_user_strategy_setting(test_user_id, 'max_expiry_days', 2)
    session_manager.update_user_strategy_setting(test_user_id, 'trade_amount', 25.0)
    
    updated_settings = session_manager.get_user_strategy_settings(test_user_id)
    expected_updated = {
        'min_win_rate': 90.0,
        'max_expiry_days': 2,
        'trade_amount': 25.0
    }
    assert updated_settings == expected_updated, f"更新後設定不正確: {updated_settings}"
    print("✅ 策略設定更新成功")
    
    # 測試重置策略設定
    print("4. 測試重置策略設定...")
    session_manager.reset_user_strategy_settings(test_user_id)
    reset_settings = session_manager.get_user_strategy_settings(test_user_id)
    assert reset_settings == expected_defaults, f"重置後設定不正確: {reset_settings}"
    print("✅ 策略設定重置成功")
    
    print("✅ 用戶策略設定管理測試通過！\n")

def test_market_scanner_integration():
    """測試MarketScanner與用戶設定的整合"""
    print("=== 測試MarketScanner用戶設定整合 ===")
    
    # 模擬MarketScanner的過濾器更新功能
    class MockMarketFilters:
        def __init__(self):
            self.min_price_threshold = 0.85
            self.max_time_to_expiry = 24.0
    
    class MockMarketScanner:
        def __init__(self):
            self.filters = MockMarketFilters()
        
        def _update_filters_from_user_settings(self, user_settings):
            min_win_rate = user_settings.get("min_win_rate", 85.0)
            self.filters.min_price_threshold = min_win_rate / 100.0
            
            max_expiry_days = user_settings.get("max_expiry_days", 1)
            self.filters.max_time_to_expiry = float(max_expiry_days * 24)
    
    scanner = MockMarketScanner()
    
    # 測試用戶設定
    user_settings = {
        'min_win_rate': 90.0,
        'max_expiry_days': 2,
        'trade_amount': 25.0
    }
    
    print("1. 測試過濾器更新...")
    scanner._update_filters_from_user_settings(user_settings)
    
    assert scanner.filters.min_price_threshold == 0.9, f"勝率閾值不正確: {scanner.filters.min_price_threshold}"
    assert scanner.filters.max_time_to_expiry == 48.0, f"到期時間不正確: {scanner.filters.max_time_to_expiry}"
    print("✅ 過濾器更新成功")
    
    print("✅ MarketScanner用戶設定整合測試通過！\n")

def test_button_callback_data():
    """測試按鈕回調數據的正確性"""
    print("=== 測試按鈕回調數據 ===")
    
    # 測試主選單按鈕
    main_menu = BotKeyboards.get_main_menu()
    expected_callbacks = [
        "strategy_bot", "copy_bot",
        "my_wallet", "performance_history", 
        "settings", "help_center"
    ]
    
    actual_callbacks = []
    for row in main_menu.inline_keyboard:
        for button in row:
            actual_callbacks.append(button.callback_data)
    
    for expected in expected_callbacks:
        assert expected in actual_callbacks, f"缺少回調數據: {expected}"
    
    print("✅ 主選單按鈕回調數據正確")
    
    # 測試策略設定按鈕
    settings_menu = BotKeyboards.get_strategy_settings_menu()
    expected_settings_callbacks = [
        "set_win_rate", "set_expiry_days", "set_trade_amount",
        "reset_strategy_defaults", "save_strategy_settings"
    ]
    
    settings_callbacks = []
    for row in settings_menu.inline_keyboard:
        for button in row:
            settings_callbacks.append(button.callback_data)
    
    for expected in expected_settings_callbacks:
        assert expected in settings_callbacks, f"缺少策略設定回調數據: {expected}"
    
    print("✅ 策略設定按鈕回調數據正確")
    print("✅ 按鈕回調數據測試通過！\n")

def main():
    """運行所有測試"""
    print("🚀 開始測試新UI功能...")
    print("=" * 50)
    
    try:
        test_keyboard_layouts()
        test_user_strategy_settings()
        test_market_scanner_integration()
        test_button_callback_data()
        
        print("🎉 所有測試通過！")
        print("✅ 新UI功能已準備就緒")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
